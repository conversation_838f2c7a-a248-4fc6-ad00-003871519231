using Binit.Framework;
using Binit.Framework.Constants.Authentication;
using Binit.Framework.Constants.SeedEntities;
using Binit.Framework.ExceptionHandling.Types;
using Binit.Framework.Helpers;
using Binit.Framework.Helpers.Excel;
using Binit.Framework.Interfaces.DAL;
using Binit.Framework.Interfaces.Email;
using Binit.Framework.Interfaces.Configuration;
using Binit.Framework.Interfaces.ExceptionHandling;
using Binit.Framework.Helpers.Email.DTOs;
using DevExtreme.AspNet.Data;
using DevExtreme.AspNet.Data.ResponseModel;
using DevExtreme.AspNet.Mvc;
using Domain.Entities.Model;
using Domain.Entities.Model.Enum;
using Domain.Entities.Model.Views;
using Domain.Logic.BusinessLogic.DTOs;
using Domain.Logic.BusinessLogic.DTOs.HenReportDTOs;
using Domain.Logic.DTOs;
using Domain.Logic.DTOs.Export;
using Domain.Logic.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Query;
using Microsoft.Extensions.Localization;
using Newtonsoft.Json;
using Quartz;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using WebApp.Models;
using WebApp.WebTools.DataGrid;
using JsLang = Binit.Framework.Localization.LocalizationConstants.WebApp.Js;
using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Controllers.HenReportController;
using EmailLang = Binit.Framework.Localization.LocalizationConstants.BinitFramework.Helpers.Email.Views;

namespace WebApp.Controllers
{
    [Authorize]
    public class HenReportController : Controller
    {
        private readonly ICapacityUnitBusinessLogic capacityUnitBusinessLogic;
        private readonly ICapacityUnitService capacityUnitService;
        private readonly ICasualtyReasonService casualtyReasonService;
        private readonly IContainerService<Container> containerService;
        private readonly IExceptionManager exceptionManager;
        private readonly IHenBatchBusinessLogic henBatchBusinessLogic;
        private readonly IHenBatchService henBatchService;
        private readonly IHenReportBusinessLogic henReportBusinessLogic;
        private readonly IHenReportService henReportService;
        private readonly IHenWarehouseService henWarehouseService;
        private readonly IMaterialService materialService;
        private readonly IMaterialTypeService materialTypeService;
        private readonly IStringLocalizer<SharedResources> localizer;
        private readonly IOperationContext operationContext;
        private readonly IService<TenantConfiguration> tenantConfigurationService;
        private readonly RazorViewRender razorViewRender;
        private readonly IHenBatchPerformanceService henBatchPerformanceService;
        private readonly ICreateHenReports createHenReports;
        private readonly IServiceTenantDependent<ReportRectification> reportRectificationService;
        private readonly IServiceTenantDependent<HenReportBulkLoadEvent> eventService;
        private readonly IServiceTenantDependent<TenantDependentEntityFile> fileService;
        private readonly IEmailSender emailSender;
        private readonly IUserService<ApplicationUser> userService;
        private readonly IFarmService farmService;
        private readonly IGeneralConfiguration generalConfiguration;

        public HenReportController(
            ICapacityUnitBusinessLogic capacityUnitBusinessLogic,
            ICapacityUnitService capacityUnitService,
            ICasualtyReasonService casualtyReasonService,
            IContainerService<Container> containerService,
            IExceptionManager exceptionManager,
            IHenBatchBusinessLogic henBatchBusinessLogic,
            IHenReportService henReportService,
            IHenReportBusinessLogic henReportBusinessLogic,
            IHenBatchService henBatchService,
            IHenWarehouseService henWarehouseService,
            IMaterialService materialService,
            IMaterialTypeService materialTypeService,
            IStringLocalizer<SharedResources> localizer,
            IOperationContext operationContext,
            IService<TenantConfiguration> tenantConfigurationService,
            RazorViewRender razorViewRender,
            IHenBatchPerformanceService henBatchPerformanceService,
            ICreateHenReports createHenReports,
            IServiceTenantDependent<ReportRectification> reportRectificationService,
            IServiceTenantDependent<HenReportBulkLoadEvent> eventService,
            IServiceTenantDependent<TenantDependentEntityFile> fileService,
            IEmailSender emailSender,
            IUserService<ApplicationUser> userService,
            IFarmService farmService,
            IGeneralConfiguration generalConfiguration)
        {
            this.capacityUnitBusinessLogic = capacityUnitBusinessLogic;
            this.capacityUnitService = capacityUnitService;
            this.casualtyReasonService = casualtyReasonService;
            this.containerService = containerService;
            this.exceptionManager = exceptionManager;
            this.henBatchBusinessLogic = henBatchBusinessLogic;
            this.henBatchService = henBatchService;
            this.henReportBusinessLogic = henReportBusinessLogic;
            this.henReportService = henReportService;
            this.henWarehouseService = henWarehouseService;
            this.materialService = materialService;
            this.materialTypeService = materialTypeService;
            this.localizer = localizer;
            this.operationContext = operationContext;
            this.tenantConfigurationService = tenantConfigurationService;
            this.razorViewRender = razorViewRender;
            this.henBatchPerformanceService = henBatchPerformanceService;
            this.createHenReports = createHenReports;
            this.reportRectificationService = reportRectificationService;
            this.eventService = eventService;
            this.fileService = fileService;
            this.emailSender = emailSender;
            this.userService = userService;
            this.farmService = farmService;
            this.generalConfiguration = generalConfiguration;
        }

        #region Index
        public IActionResult Index(HenStage? henStage = null, Guid? henBatchId = null, string validationError = null, string message = null)
        {
            if (!this.operationContext.UserIsInRole(Roles.BackofficeSuperAdministrator))
            {
                if (!henStage.HasValue)
                {
                    if (CheckAuthorization(HenStage.Laying))
                        henStage = HenStage.Laying;
                    else if (CheckAuthorization(HenStage.Breeding))
                        henStage = HenStage.Breeding;
                    else
                        return Forbid();
                }
                else
                {
                    if (!CheckAuthorization(henStage))
                        return Forbid();
                }
            }
            if (henStage.HasValue)
                ViewData["HenStage"] = henStage.Value;

            ViewData["Title"] = localizer[Lang.IndexTitle] + GetHenStageString(henStage);
            // This is required in order to localize datatables.
            ViewData["DatatableResources"] = JsLocalizer.GetLocalizedResources(JsLang.Datatables, this.localizer);
            ViewData["HenStageDefaultOption"] = henStage.HasValue ? (henStage == HenStage.Laying ? localizer[Lang.LayingHenBatch].Value : localizer[Lang.BreedingHenBatch].Value) : (string)null;
            ViewData["TableId"] = $"hen-report-grid-{henStage}";
            ViewData["HasClusters"] = tenantConfigurationService.GetAll().Any(tc => tc.TenantId == operationContext.GetUserTenantId() && tc.TenantConfigurationEnum == TenantConfigurationEnum.Clusters && tc.Value == Boolean.TrueString);

            InitLists(henStage, henBatchId);
            if (validationError == "1")
                ViewData["ValidationError"] = localizer[Lang.NoStorageHenBatchAvailable].Value;
            else if (validationError == "2")
                ViewData["ValidationError"] = localizer[Lang.PendingHenReports].Value;
            else if (validationError == "3")
                ViewData["ValidationError"] = localizer[Lang.CloseHenbatch].Value;

            if (!string.IsNullOrEmpty(message))
                ViewData["Message"] = message;

            return View();
        }

        public async Task<IActionResult> GetAll(DataSourceLoadOptions loadOptions, Dictionary<string, string> filters)
        {
            loadOptions.MapToEntity(new Dictionary<string, string>() {
                {"uploadEnumName", "uploadEnum"}
            });

            IQueryable<HenReportGridView> query = henReportBusinessLogic.GetAllForIndex(filters);

            List<Guid> rectificationReportId = reportRectificationService.GetAll(false, false)
                                                   .Where(rr => rr.AuthorizationResponseEnum == AuthorizationResponseEnum.Pending)
                                                   .Select(rr => rr.RectificationHenReportId)
                                                   .ToList();
            if (rectificationReportId.Count > 0)
            {
                query = query.Where(hr => !rectificationReportId.Any(rr => rr == hr.Id));
            }

            LoadResult result = await IgniteDataSourceLoader<HenReportGridView, HenReportRow>.LoadAsync(query, loadOptions);
            if (result.data is List<HenReportRow> rows)
            {
                var performances = henBatchPerformanceService.GetAll().Select(hbp => new { hbp.HenBatchId, hbp.Date });

                rows.ForEach(r =>
                {
                    r.CurrentWeek = performances.Where(p => p.HenBatchId == r.HenBatchId || p.HenBatchId == r.HenBatchParentId).Select(h => h.Date).Max(hbp => hbp.Date) <= r.Date;
                    r.HenStage = EnumHelper<HenStage>.GetDisplayName(r.HenStageEnum, localizer);
                    r.Status = r.HenBatchActiveBoolean ? localizer[Lang.ActiveHenBatch].Value : localizer[Lang.InActiveHenBatch].Value;
                    r.UploadEnumName = EnumHelper<ReportUploadEnum>.GetDisplayName(r.UploadEnum, localizer);
                });
            }

            return Ok(result);
        }
        #endregion

        #region Export
        public async Task<IActionResult> Export(
            DataSourceLoadOptions loadOptions,
            Dictionary<string, string> filters,
            [FromServices] IScheduler scheduler,
            [FromServices] ISpreadSheetExportService spreadSheetExportService)
        {
            filters.Add("entity", nameof(HenReportExport));
            filters.Add("host", Request.Host.Value);
            filters.Add("scheme", Request.Scheme);

            loadOptions.MapToEntity(new Dictionary<string, string>() {
                {"uploadEnumName", "uploadEnum"}
            });

            int totalCount = int.Parse(filters["totalCount"]);

            if (totalCount < 2000)
            {
                var (uri, file) = await spreadSheetExportService.Export(loadOptions, filters);
                return Created(uri, new { file });
            }
            else
            {
                string loadOptionsJson = JsonConvert.SerializeObject(loadOptions);
                string filtersJson = JsonConvert.SerializeObject(filters);
                string email = operationContext.GetUsername();

                JobDataMap data = new JobDataMap
                {
                    ["loadOptions"] = loadOptionsJson,
                    ["filters"] = filtersJson,
                    ["email"] = email,
                    ["culture"] = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName

                };
                await scheduler.TriggerJob(new JobKey("ExportJob", "Startup"), data);

                return Accepted(new
                {
                    message = localizer.GetString(
                    Binit.Framework.Localization.LocalizationConstants.DomainLogic.JobScheduler.Jobs.ExportJob.ExportResultMessage,
                    email).Value
                });
            }
        }
        #endregion

        #region Create
        public IActionResult Create(HenStage? henStage = null, Guid? henBatchId = null)
        {
            if (henStage.HasValue)
            {
                if (!CheckAuthorization(henStage.Value))
                    return Forbid();
            }
            else
                return View("Index");


            ViewData["Title"] = localizer[Lang.CreateTitle] + GetHenStageString(henStage);
            ViewData["Action"] = "Create";
            ViewData["HenReportResources"] = JsLocalizer.GetLocalizedResources(JsLang.HenReportCreateOrEdit, this.localizer);

            HenBatchFilterDTO data = new HenBatchFilterDTO()
            {
                HenStage = henStage,
                CurrentBatchId = henBatchId
            };

            string validationError = InitCreateLists(data);
            InitCapacityUnitLists(henStage.Value);

            IQueryable<TenantConfiguration> configs = GetTenantConfigurations();

            HenReportViewModel model;
            if (henBatchId.HasValue)
            {
                HenBatch henBatch = henBatchService.GetFull(henBatchId.Value);
                model = new HenReportViewModel(henBatch, configs)
                {
                    FromHenBatch = true
                };
            }
            else model = new HenReportViewModel(henStage.Value, configs);

            model.Eggs = GetEggsMaterials();

            if (validationError == "1")
                return View("Index", new { validationError });

            return View("CreateOrEdit", model);
        }

        public IQueryable<TenantConfiguration> GetTenantConfigurations()
        {

            IQueryable<TenantConfiguration> configs = tenantConfigurationService.GetAll().Where(c => c.TenantId == operationContext.GetUserTenantId());

            ViewData["HasClusters"] = configs.Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.Clusters && s.Value == "True");
            ViewData["AutomaticallySentEggs"] = configs.Any(c => c.TenantConfigurationEnum == TenantConfigurationEnum.AutomaticallySentEggs && c.Value == "True");
            ViewData["DepopulateBreeding"] = configs.Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.DepopulateBreeding && s.Value == "True");
            ViewData["ToCageToFloorBreeding"] = configs.Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.ToCageToFloorBreeding && s.Value == "True");
            ViewData["DepopulateLaying"] = configs.Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.DepopulateLaying && s.Value == "True");
            ViewData["ToCageToFloorLaying"] = configs.Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.ToCageToFloorLaying && s.Value == "True");

            return configs;
        }

        [HttpPost]
        public async Task<IActionResult> Create(HenReportViewModel viewModel)
        {
            if (!CheckAuthorization(viewModel.HenStage))
                return Forbid();

            Validate(viewModel);

            if (ModelState.IsValid)
            {
                try
                {
                    HenReport currentHenReport = viewModel.MapViewModel();
                    await henReportBusinessLogic.CreateAsync(
                        currentHenReport,
                        viewModel.ToFeedIntakeDTO());

                    if (currentHenReport.DeadFemale + currentHenReport.DeadMale + currentHenReport.DepopulateFemale + currentHenReport.DepopulateMale == 0)
                        return RedirectToAction("Index", new { henStage = viewModel.HenStage });

                    List<string> henReports = new List<string>() { currentHenReport.Id.ToString() };

                    return RedirectToAction("EstablishDeathAndDepopulationQuantitiesReasons", new { henReportsId = henReports, henStage = viewModel.HenStage });
                }
                catch (ValidationException ex)
                {
                    foreach (KeyValuePair<string, string> error in ex.Errors)
                        ModelState.AddModelError(string.Empty, error.Value);
                }
            }

            ViewData["Title"] = localizer[Lang.CreateTitle] + GetHenStageString(viewModel.HenStage);
            ViewData["Action"] = "Create";
            ViewData["HenReportResources"] = JsLocalizer.GetLocalizedResources(JsLang.HenReportCreateOrEdit, this.localizer);

            InitCapacityUnitLists(viewModel.HenStage);
            GetTenantConfigurations();
            HenBatchFilterDTO data = viewModel.HenBatchId != null
                ? viewModel.ToFilterDTO()
                : new HenBatchFilterDTO() { HenStage = viewModel.HenStage };

            InitCreateLists(data);

            if (viewModel.HenBatchId != null)
                viewModel.HenBatch = henBatchService.GetFull(new Guid(viewModel.HenBatchId));

            return View("CreateOrEdit", viewModel);
        }
        #endregion

        #region Create from Warehouse
        public IActionResult CreateFromWarehouse(HenStage? henStage = null, Guid? warehouseId = null, string validationError = "")
        {
            if (henStage.HasValue)
            {
                if (!CheckAuthorization(henStage.Value))
                    return Forbid();
            }
            else
                return View("Index");


            ViewData["Title"] = localizer[Lang.CreateTitle] + GetHenStageString(henStage);
            ViewData["HenReportResources"] = JsLocalizer.GetLocalizedResources(JsLang.HenReportCreateOrEdit, this.localizer);

            HenBatchFilterDTO data = new HenBatchFilterDTO()
            {
                HenStage = henStage,
                SelectedWarehouse = warehouseId
            };

            validationError = InitCreateLists(data);
            InitCapacityUnitLists(henStage.Value);
            IQueryable<TenantConfiguration> configs = GetTenantConfigurations();
            WarehouseHenReportViewModel model = warehouseId.HasValue
                ? new WarehouseHenReportViewModel(henWarehouseService.GetFull(warehouseId.Value), configs)
                : new WarehouseHenReportViewModel(henStage.Value, new TenantConfigurationDTO(configs));

            model.Eggs = GetEggsMaterials();

            if (validationError == "1")
                return RedirectToAction("Index", new { henStage, validationError });

            return View("CreateFromWarehouse", model);
        }

        [HttpPost]
        public async Task<IActionResult> CreateFromWarehouse(WarehouseHenReportViewModel viewModel, [FromServices] ICreateHenReports createHenReports)
        {
            if (ModelState.IsValid)
            {
                //validate view model and set relative values
                await ValidateFromWarehouse(viewModel);

                if (ModelState.IsValid)
                {
                    try
                    {
                        string alert = viewModel.Eggs != null && viewModel.Eggs.Any(e => e.Quantity > 0) ?
                            AlertPruductionDate(new Guid(viewModel.LineReports.FirstOrDefault().HenBatchId)) : default;

                        IEnumerable<Guid> henReports = await createHenReports.CreateWarehouseHenReports(
                            viewModel.ToDTO(),
                            viewModel.ToEntity(),
                            viewModel.Eggs ?? new List<HenReportEggsDTO>());

                        if (henReports.Count() == 0)
                            return RedirectToAction("Index", new { henStage = viewModel.HenStage, message = string.IsNullOrEmpty(alert) ? null : alert });

                        return RedirectToAction("EstablishDeathAndDepopulationQuantitiesReasons", new { henReportsId = henReports.Select(id => id.ToString()).ToList(), henStage = viewModel.HenStage, message = string.IsNullOrEmpty(alert) ? null : alert, fromCreation = true });
                    }
                    catch (ValidationException ex)
                    {
                        foreach (KeyValuePair<string, string> error in ex.Errors)
                            ModelState.AddModelError(string.Empty, error.Value);
                    }
                }
            }

            ViewData["Title"] = localizer[Lang.CreateTitle] + GetHenStageString(viewModel.HenStage);
            ViewData["HenReportResources"] = JsLocalizer.GetLocalizedResources(JsLang.HenReportCreateOrEdit, this.localizer);

            GetTenantConfigurations();
            InitCapacityUnitLists(viewModel.HenStage);

            HenBatchFilterDTO data = viewModel.ToFilterDTO();
            // set feed intake origins
            if (!string.IsNullOrEmpty(viewModel.WarehouseId))
            {
                List<HenBatch> henBatches = henBatchService.GetAllFromWarehouse(new Guid(viewModel.WarehouseId)).ToList();
                foreach (HenBatchHenReportViewModel hr in viewModel.LineReports)
                {
                    if (viewModel.HenStage == HenStage.Laying)
                    {
                        hr.FeedIntakeFemaleOrigins = GetFeedIntakeOrigins(Guid.Parse(hr.HenBatchId), viewModel.HenStage, true, string.IsNullOrEmpty(hr.FeedIntakeFemaleOriginId) ? (Guid?)null : Guid.Parse(hr.FeedIntakeFemaleOriginId));
                        hr.FeedIntakeMaleOrigins = GetFeedIntakeOrigins(Guid.Parse(hr.HenBatchId), viewModel.HenStage, false, string.IsNullOrEmpty(hr.FeedIntakeMaleOriginId) ? (Guid?)null : Guid.Parse(hr.FeedIntakeMaleOriginId));
                    }
                    else
                        hr.FeedIntakeOrigins = GetFeedIntakeOrigins(Guid.Parse(hr.HenBatchId), viewModel.HenStage, true, string.IsNullOrEmpty(hr.FeedIntakeOriginId) ? (Guid?)null : Guid.Parse(hr.FeedIntakeOriginId));
                }
            }
            InitCreateLists(data);

            if (viewModel.Eggs == null)
                viewModel.Eggs = GetEggsMaterials();
            else
                viewModel.Eggs.AddRange(GetEggsMaterials(viewModel.Eggs.Select(e => e.EggId).ToList()));
            return View("CreateFromWarehouse", viewModel);
        }

        public IActionResult CreateBreeding()
        {
            ViewData["Title"] = localizer[Lang.CreateTitle] + GetHenStageString(HenStage.Breeding);

            // load farms list
            InitCreateLists(new HenBatchFilterDTO()
            {
                HenStage = HenStage.Breeding,
            });

            return View("CreateBreeding");
        }

        public IActionResult CreateLaying()
        {
            ViewData["Title"] = localizer[Lang.CreateTitle] + GetHenStageString(HenStage.Laying);

            // load farms list
            InitCreateLists(new HenBatchFilterDTO()
            {
                HenStage = HenStage.Laying,
            });

            return View("CreateLaying");
        }


        [HttpPost]
        public async Task<IActionResult> CreateHenReportsFromTable([FromBody] CreateHenReportFromTableDTO createHenReportFromTableDTO)
        {
            if (createHenReportFromTableDTO == null)
                return BadRequest(new { message = "Verifique se os campos estão preenchidos corretamente." });

            CultureInfo culture = CultureInfo.CurrentUICulture;
            DateTime reportDate = Convert.ToDateTime(createHenReportFromTableDTO.ReportDate, culture);

            try
            {
                IEnumerable<HenReportResultDTO> henReports = await createHenReports.CreateHenReportsFromTable(reportDate, createHenReportFromTableDTO.Reports);
                return Ok(new { henReports });
            }
            catch (ValidationException ex)
            {
                if (ex.Errors != null && ex.Errors.Any())
                    return BadRequest(new { message = string.Join(", ", ex.Errors.Select(e => e.Value)) });

                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Erro ao criar lançamentos" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> CreateBreedingReportsFromTable([FromBody] CreateBreedingReportFromTableDTO createBreedingReportFromTableDTO)
        {
            if (createBreedingReportFromTableDTO == null)
                return BadRequest(new { message = "Verifique se os campos estão preenchidos corretamente." });

            CultureInfo culture = CultureInfo.CurrentUICulture;
            DateTime reportDate = Convert.ToDateTime(createBreedingReportFromTableDTO.ReportDate, culture);

            try
            {
                // Validate that we're only getting breeding reports
                foreach (var report in createBreedingReportFromTableDTO.Reports)
                {
                    // Get the HenBatch to verify it's a breeding stage
                    var henBatch = henBatchService.Get(report.HenBatchId);
                    if (henBatch.HenStage != HenStage.Breeding)
                    {
                        return BadRequest(new { message = "Apenas lotes de reprodução podem ser enviados para este endpoint." });
                    }
                }

                IEnumerable<HenReportResultDTO> henReports = await createHenReports.CreateBreedingReportsFromTable(reportDate, createBreedingReportFromTableDTO.Reports);
                return Ok(new { henReports });
            }
            catch (ValidationException ex)
            {
                if (ex.Errors != null && ex.Errors.Any())
                    return BadRequest(new { message = string.Join(", ", ex.Errors.Select(e => e.Value)) });

                return BadRequest(new { message = ex.Message });
            }
            catch (Exception)
            {
                return StatusCode(500, new { message = "Erro ao criar lançamentos" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> CreateLayingReportsFromTable([FromBody] CreateLayingReportFromTableDTO createLayingReportFromTableDTO)
        {
            if (createLayingReportFromTableDTO == null)
                return BadRequest(new { message = "Verifique se os campos estão preenchidos corretamente." });

            CultureInfo culture = CultureInfo.CurrentUICulture;
            DateTime reportDate = Convert.ToDateTime(createLayingReportFromTableDTO.ReportDate, culture);

            try
            {
                // Validate that we're only getting laying reports
                foreach (var report in createLayingReportFromTableDTO.Reports)
                {
                    // Get the HenBatch to verify it's a laying stage
                    var henBatch = henBatchService.Get(report.HenBatchId);
                    if (henBatch.HenStage != HenStage.Laying)
                    {
                        return BadRequest(new { message = "Apenas lotes de postura podem ser enviados para este endpoint." });
                    }
                }

                // Process egg data for each report
                foreach (var report in createLayingReportFromTableDTO.Reports)
                {
                    // Create ClassifiedEggs array for the HenReportClassifiedEgg entries
                    report.ClassifiedEggs = new List<ClassifiedEggDTO>();

                    // Add egg types with their exact MaterialIds
                    void AddEggType(Guid materialId, int quantity)
                    {
                        if (quantity <= 0) return;
                        report.ClassifiedEggs.Add(new ClassifiedEggDTO
                        {
                            MaterialId = materialId,
                            Quantity = quantity
                        });
                    }

                    // Add all egg types - Hatchable Eggs (Incubáveis)
                    if (report.CleanNestEggs > 0)
                        AddEggType(new Guid("C355B802-FBB0-4969-45CC-08D8FABC0A06"), report.CleanNestEggs); // Ninho Limpo - Hatchable
                    if (report.DirtyNestEggs > 0)
                        AddEggType(new Guid("D2006215-43F0-40B3-45CD-08D8FABC0A06"), report.DirtyNestEggs); // Ninho Sujo - Hatchable
                    if (report.BedEggs > 0)
                        AddEggType(new Guid("90D811EE-4FC1-435E-45CE-08D8FABC0A06"), report.BedEggs); // Cama - Hatchable

                    // Commercial Eggs (Comerciais)
                    if (report.DoubleYolkEggs > 0)
                        AddEggType(new Guid("D6545A08-82B7-4D7C-45CF-08D8FABC0A06"), report.DoubleYolkEggs); // Duplo - Commercial
                    if (report.SmallEggs > 0)
                        AddEggType(new Guid("FDF5D866-22F0-4F26-45D0-08D8FABC0A06"), report.SmallEggs); // Pequeno - Commercial
                    if (report.DefectiveEggs > 0)
                        AddEggType(new Guid("CEA6A1CC-65F4-45ED-45D1-08D8FABC0A06"), report.DefectiveEggs); // Defeituoso - Commercial
                    if (report.DirtyRolledEggs > 0)
                        AddEggType(new Guid("99B49505-9740-46E0-45D2-08D8FABC0A06"), report.DirtyRolledEggs); // Sujo Rolado - Commercial
                    if (report.CrackedEggs > 0)
                        AddEggType(new Guid("8B541005-28E6-44CC-45D3-08D8FABC0A06"), report.CrackedEggs); // Trincado - Commercial
                    if (report.ThinShellEggs > 0)
                        AddEggType(new Guid("60DB4E62-4F18-41ED-45D6-08D8FABC0A06"), report.ThinShellEggs); // Casca Fina - Commercial

                    // Eliminated/Broken Eggs (Eliminado/Quebrado)
                    if (report.EliminatedBrokenEggs > 0)
                        AddEggType(new Guid("7B1BC3D9-B32B-4A42-45D4-08D8FABC0A06"), report.EliminatedBrokenEggs); // Eliminado/Quebrado
                }

                IEnumerable<HenReportResultDTO> henReports = await createHenReports.CreateLayingReportsFromTable(reportDate, createLayingReportFromTableDTO.Reports);
                return Ok(new { henReports });
            }
            catch (ValidationException ex)
            {
                if (ex.Errors != null && ex.Errors.Any())
                    return BadRequest(new { message = string.Join(", ", ex.Errors.Select(e => e.Value)) });

                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Erro ao criar lançamentos: " + ex.Message });
            }
        }

        [HttpGet]
        public IActionResult BulkLoads(HenStage? henStage)
        {
            if (!henStage.HasValue ||
                    !operationContext.UserIsInRole(Roles.BackofficeSuperAdministrator) &&
                        ((henStage == HenStage.Breeding && !operationContext.UserIsInRole(Roles.BackofficeBreedingBulkLoad)) ||
                        (henStage == HenStage.Laying && !operationContext.UserIsInRole(Roles.BackofficeLayingBulkLoad))))
                return Forbid();

            ViewData["HenStage"] = henStage;
            ViewData["Title"] = localizer.GetString(Lang.BulkLoadsTitle, GetHenStageString(henStage));
            ViewData["DatatableResources"] = JsLocalizer.GetLocalizedResources(JsLang.Datatables, this.localizer);

            return View();
        }

        [HttpGet]
        public async Task<IActionResult> GetBulkLoads(DataSourceLoadOptions loadOptions)
        {
            IIncludableQueryable<HenReportBulkLoadEvent, HenBatch> reports = eventService.GetAll().Include(e => e.User).Include(e => e.HenBatch);
            LoadResult response = await DataSourceLoader.LoadAsync(reports, loadOptions);
            IEnumerable<BulkLoadRow> rows = response.data.Cast<HenReportBulkLoadEvent>()
                .Select(e => new BulkLoadRow()
                {
                    DT_RowId = e.Id.ToString(),
                    Date = e.CreatedDate,
                    User = e.User.Name,
                    HenBatch = e.HenBatch.Code,
                    Status = EnumHelper<HenReportBulkLoadStatusEnum>.GetDisplayName(e.Status, localizer)
                });
            return Json(rows);
        }

        public async Task<IActionResult> BulkLoad(HenStage? henStage, Guid id)
        {
            if (!henStage.HasValue ||
                !operationContext.UserIsInRole(Roles.BackofficeSuperAdministrator) &&
                    ((henStage == HenStage.Breeding && !operationContext.UserIsInRole(Roles.BackofficeBreedingBulkLoad)) ||
                    (henStage == HenStage.Laying && !operationContext.UserIsInRole(Roles.BackofficeLayingBulkLoad))))
                return Forbid();

            IQueryable<HenReportBulkLoadEvent> @event = eventService.GetAll().Where(e => e.Id == id);
            IQueryable<TenantDependentEntityFile> files = fileService.GetAll().Where(f => f.TenantDependentEntityId == id);
            BulkLoadViewModel model = await BulkLoadViewModel.MapQuery(@event, files, localizer);
            model.HenStage = henStage.Value;
            return View(model);
        }

        [HttpPost]
        public async Task<IActionResult> Import()
        {
            try
            {
                IFormFile formFile = this.Request.Form.Files.FirstOrDefault();
                await henReportBusinessLogic.Import(formFile);
            }
            catch (Exception e)
            {
                this.HttpContext.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                return Json(e.Message);
            }

            return Ok();
        }

        //Get hen batches for warehouse hen report
        [HttpPost]
        public async Task<IActionResult> GetBatchesRow(string warehouseId, string date, TenantConfigurationDTO tenantConfigDTO, HenStage henStage)
        {
            List<HenBatch> henBatches = henBatchService.GetAllFromWarehouse(new Guid(warehouseId)).OrderBy(hb => hb.Line.Code).ToList();
            WarehouseHenReportViewModel model = new WarehouseHenReportViewModel(henStage, tenantConfigDTO);
            string errorMessage = string.Empty;
            var errors = new List<string>();

            // validate hen batches and add them to the model
            foreach (HenBatch hb in henBatches)
            {
                errorMessage = ValidateHenBatch(hb, date);
                if (string.IsNullOrEmpty(errorMessage))
                {
                    IEnumerable<Guid> formulasConsumed = hb.FormulasConsumed.Select(fc => fc.FormulaId);
                    if (henStage == HenStage.Laying)
                    {
                        model.LineReports.Add(new HenBatchHenReportViewModel(hb)
                        {
                            FeedIntakeFemaleOrigins = GetFeedIntakeOrigins(hb.Id, henStage, true),
                            FeedIntakeMaleOrigins = GetFeedIntakeOrigins(hb.Id, henStage, false)

                        });
                    }
                    else
                    {
                        model.LineReports.Add(new HenBatchHenReportViewModel(hb)
                        {
                            FeedIntakeOrigins = GetFeedIntakeOrigins(hb.Id, henStage, true)

                        });
                    }

                }
                else
                {
                    errors.Add(errorMessage);
                }
            }

            if (model.LineReports.Count == 0)
            {
                errors.Add(localizer[Lang.NoSuitableBatches].Value);
            }

            return Ok(new
            {
                errors = string.Join("<br/>", errors),
                index = model.LineReports.Count,
                femaleBirds = model.LineReports.Any(l => l.HenAmountFemale > 0),
                birdsCard = await razorViewRender.RenderToStringAsync("Shared/Components/WarehouseHenReportCards/Default", model, new Dictionary<string, object>() { { "model", model }, { "index", 0 }, { "units", capacityUnitService.GetByMeasurementUnitType(MeasurementUnitType.Unit).OrderBy(t => t.RelativeValue).Select(cu => new SelectListItem(cu.Symbol, cu.Id.ToString(), cu.RelativeValue == 1)).ToList() }, { "showAll", true }, { "cardName", "Birds" } }),
                feedIntakeCard = await razorViewRender.RenderToStringAsync("Shared/Components/WarehouseHenReportCards/Default", model, new Dictionary<string, object>() { { "model", model }, { "index", 0 }, { "units", ListWithKilogramsAndGAD(selectKgByDefault: true) }, { "showAll", true }, { "cardName", "FeedIntake" } })
            });
        }

        [HttpGet]
        public decimal CalculateGAD(string value, Guid measure, Guid henBatchId, bool? female = null)
        {
            return henReportBusinessLogic.CalculateGAD(value, measure, henBatchId, female);
        }

        public IActionResult CalculateGADToKG(string value, Guid henBatchId, bool? female = null)
        {
            return Json(new { data = CalculateGAD(value, CapacityUnits.Kilograms, henBatchId) });
        }

        [HttpGet]
        public IActionResult CheckBatchCapitalizationAlert(Guid henBatchId, bool hasEggData = false)
        {
            // Only show the alert if egg data is being registered
            if (!hasEggData)
            {
                return Content("");
            }

            string alertMessage = AlertPruductionDate(henBatchId);
            return Content(alertMessage);
        }

        private string AlertPruductionDate(Guid henBatchId)
        {
            bool[] alert = henReportBusinessLogic.AlertPruductionDate(henBatchId);
            if (alert.All(a => a == true))
                return $"{localizer[Lang.AlertProductionDateMessage]}<br>{localizer[Lang.AlertCapitalizationDateMessage]}";
            else if (alert.Any(a => a == true))
                return localizer[Lang.AlertCapitalizationDateMessage];
            else
                return "";
        }
        #endregion

        #region Edit
        public async Task<IActionResult> Edit(Guid henReportId, bool shouldValidateCreateAndApprove = true)
        {
            IQueryable<HenReport> query = henReportService.GetAllFullReports(henReportId);
            HenReport henReportEntity = query.FirstOrDefault();
            bool hasNoOriginFemale = !henReportEntity.ShippingNotes.Any(sn => sn.Name == HenReportSteps.ConsumedFoodFemaleStep) && henReportEntity.FeedIntakeFemale != 0;
            bool hasNoOriginMale = !henReportEntity.ShippingNotes.Any(sn => sn.Name == HenReportSteps.ConsumedFoodMaleStep) && henReportEntity.FeedIntakeMale != 0;

            if (query.Any(hr => hr.HenBatch.DateEnd != null || !hr.HenBatch.Active))
                return RedirectToAction("Index", "HenReport", new { henStage = query.Select(hr => hr.HenBatch.HenStage).FirstOrDefault(), validationError = "3" });

            IQueryable<TenantConfiguration> configs = GetTenantConfigurations();
            HenReportViewModel reportViewModel;
            if (hasNoOriginFemale || hasNoOriginMale)
            {
                HenReport henReportBefore = henReportService.GetAllFullReportsBefore(henReportEntity.Id);
                reportViewModel = await HenReportViewModel.MapQuery(query, configs, henReportBefore, hasNoOriginFemale, hasNoOriginMale);
            }
            else
            {
                reportViewModel = await HenReportViewModel.MapQuery(query, configs);
            }

            if (!CheckAuthorization(reportViewModel.HenStage))
                return Forbid();

            ViewData["Title"] = localizer[Lang.EditTitle] + GetHenStageString(reportViewModel.HenStage);
            ViewData["Action"] = "Edit";
            ViewData["HenReportResources"] = JsLocalizer.GetLocalizedResources(JsLang.HenReportCreateOrEdit, this.localizer);

            InitCapacityUnitLists(reportViewModel.HenStage, selectKgByDefault: false);
            await InitEditLists(reportViewModel.Id);

            if (reportViewModel.HenBatchId != null)
                reportViewModel.HenBatch = henBatchService.GetAll().Where(hb => hb.Id == new Guid(reportViewModel.HenBatchId))
                    .Include(hb => hb.Line).ThenInclude(l => l.Warehouse).ThenInclude(w => w.Cluster).ThenInclude(c => c.Farm)
                    .First();

            bool lastReport = henReportBusinessLogic.LastReport(henReportId, Guid.Parse(reportViewModel.HenBatchId));
            reportViewModel.CreateAndApprove = shouldValidateCreateAndApprove ? operationContext.UserIsInAnyRole(Roles.HenReportBreedingAdjustmentApprover, Roles.HenReportLayingAdjustmentApprover, Roles.BackofficeSuperAdministrator) || lastReport : false;
            reportViewModel.Eggs.AddRange(GetEggsMaterials(reportViewModel.Eggs.Select(e => e.EggId).ToList()));
            bool hasPendingReports = henReportBusinessLogic.HasPendingReports(Guid.Parse(reportViewModel.Id));

            if (hasPendingReports)
                return RedirectToAction("Index", "HenReport", new { henStage = reportViewModel.HenStage, validationError = "2" });
            else
                return View("CreateOrEdit", reportViewModel);
        }

        [HttpPost]
        public async Task<IActionResult> Edit(HenReportViewModel viewModel)
        {
            Validate(viewModel, true);
            if (ModelState.IsValid)
            {
                try
                {
                    HenReport newHenReport = viewModel.ToEntity();
                    newHenReport.Id = Guid.Empty;
                    Guid rectificationHenReportId = new Guid(viewModel.Id);
                    HenReportRectificationDTO differences = henReportBusinessLogic.GetDifferences(newHenReport, rectificationHenReportId, viewModel.HenStage);
                    differences.MaterialBatchFemaleConsumedId = !string.IsNullOrEmpty(viewModel.MaterialBatchFemaleConsumedId)
                                                                    ? Guid.Parse(viewModel.MaterialBatchFemaleConsumedId)
                                                                    : (Guid?)null;
                    differences.MaterialBatchMaleConsumedId = !string.IsNullOrEmpty(viewModel.MaterialBatchMaleConsumedId)
                                                                    ? Guid.Parse(viewModel.MaterialBatchMaleConsumedId)
                                                                    : (Guid?)null;
                    Binit.Shaper.Entities.Draft.Draft draft = viewModel.ToDraft();

                    if (viewModel.CreateAndApprove)
                        await henReportBusinessLogic.CreateAndApproveRectificationAsync(rectificationHenReportId,
                            newHenReport,
                            differences,
                            draft);
                    else
                    {
                        await henReportBusinessLogic.CreateHenReportDraft(draft,
                            rectificationHenReportId,
                            newHenReport,
                            differences);

                        Guid farmId = Guid.Parse(viewModel.FarmId);
                        var farm = farmService.GetAll().Where(w => w.Id == farmId).Select(s => new { s.Name, s.Code, s.CompanyId }).FirstOrDefault();
                        if (farm.CompanyId != null)
                        {
                            // Find admins emails by company and security profile
                            var adminEmails = userService.GetEmailsByCompanyAndSecurityProfile((Guid)farm.CompanyId, SecurityProfiles.AdmIncubatorioId);

                            if (adminEmails.Count > 0)
                            {
                                var name = $"{farm.Code} | {farm.Name.TrimEnd()}";
                                // Send email to the users
                                await emailSender.SendEmailAsync(adminEmails, localizer[EmailLang.AlertForHenReport.Subject, name], new AlertForHenReportDTO(generalConfiguration, localizer, name, DateTime.Now.ToString("dd/MM/yyyy")));
                            }
                        }

                        return RedirectToAction("Index", new { henStage = viewModel.HenStage });
                    }

                    return RedirectToAction("EstablishDeathAndDepopulationQuantitiesReasons", new { henStage = viewModel.HenStage, henReportId = newHenReport.Id });
                }
                catch (ValidationException ex)
                {
                    foreach (KeyValuePair<string, string> error in ex.Errors)
                        ModelState.AddModelError(string.Empty, error.Value);
                }
            }

            ViewData["Title"] = localizer[Lang.EditTitle] + GetHenStageString(viewModel.HenStage);
            ViewData["Action"] = "Edit";
            ViewData["HenReportResources"] = JsLocalizer.GetLocalizedResources(JsLang.HenReportCreateOrEdit, this.localizer);

            IQueryable<TenantConfiguration> configs = GetTenantConfigurations();
            viewModel.TenantConfiguration = new TenantConfigurationDTO(configs);

            InitCapacityUnitLists(viewModel.HenStage);
            await InitEditLists(viewModel.Id);

            if (viewModel.HenBatchId != null)
                viewModel.HenBatch = henBatchService.GetFull(new Guid(viewModel.HenBatchId));

            return View("CreateOrEdit", viewModel);
        }

        public async Task InitEditLists(string henReportId)
        {
            var containers = await henReportService.GetAll(asNoTracking: true).Where(hr => hr.Id == Guid.Parse(henReportId))
                .Select(hr => new
                {
                    hr.HenBatch.Line.Warehouse.FarmId,
                    FarmName = hr.HenBatch.Line.Warehouse.Farm.Name,
                    hr.HenBatch.Line.Warehouse.ClusterId,
                    ClusterName = hr.HenBatch.Line.Warehouse.Cluster.Name,
                    hr.HenBatch.Line.WarehouseId,
                    WarehouseName = hr.HenBatch.Line.Warehouse.Name,
                    hr.HenBatch.LineId,
                    LineName = hr.HenBatch.Line.Name,
                    HenBatchId = hr.HenBatchId,
                    HenStage = hr.HenBatch.HenStage
                }).FirstAsync();
            ViewData["Origins"] = containers.HenStage != HenStage.Laying ? GetFeedIntakeOrigins(containers.HenBatchId, containers.HenStage) : new List<SelectListItem>();
            ViewData["FemaleOrigins"] = containers.HenStage == HenStage.Laying ? GetFeedIntakeOrigins(containers.HenBatchId, containers.HenStage, true) : new List<SelectListItem>();
            ViewData["MaleOrigins"] = containers.HenStage == HenStage.Laying ? GetFeedIntakeOrigins(containers.HenBatchId, containers.HenStage) : new List<SelectListItem>();
            ViewData["Farms"] = new List<SelectListItem>() { new SelectListItem(containers.FarmName, containers.FarmId.ToString(), true) };
            ViewData["Clusters"] = new List<SelectListItem>() { new SelectListItem(containers.ClusterName, containers.ClusterId.ToString(), true) }; ;
            ViewData["Warehouses"] = new List<SelectListItem>() { new SelectListItem(containers.WarehouseName, containers.WarehouseId.ToString(), true) }; ;
            ViewData["Lines"] = new List<SelectListItem>() { new SelectListItem(containers.LineName, containers.LineId.ToString(), true) };
        }
        #endregion

        #region Details
        public IActionResult Details(string id, HenStage? henStage = null)
        {
            try
            {
                // Get henReport from database
                HenReport henReport = henReportService.GetAll().Where(hr => hr.Id == new Guid(id))
                    .Include(hr => hr.HenBatch).ThenInclude(hb => hb.Line).ThenInclude(l => l.Warehouse)
                    .Include(hr => hr.HenBatch).ThenInclude(hb => hb.Genetic)
                    .Include(hr => hr.ClassifiedEggs).ThenInclude(ce => ce.Material).ThenInclude(m => m.MaterialType)
                    .Include(hr => hr.Casualties).ThenInclude(ce => ce.CasualtyReason)
                    .Include(hr => hr.Depopulations).ThenInclude(ce => ce.DepopulationReason)
                    .FirstOrDefault();

                if (!CheckAuthorization(henReport.HenBatch.HenStage))
                    return Forbid();

                ViewData["Title"] = localizer[Lang.DetailsTitle] + GetHenStageString(henReport.HenBatch.HenStage);

                if (henStage.HasValue)
                    ViewData["IsBreeding"] = henStage == HenStage.Breeding;
                else
                    ViewData["IsBreeding"] = false;

                IQueryable<TenantConfiguration> configs = tenantConfigurationService.GetAll().Where(c => c.TenantId == operationContext.GetUserTenantId());

                HenReportViewModel model = new HenReportViewModel(henReport, configs)
                {
                    UndeclaredQuantitiesFemale = henReport.DeadFemale - henReport.CasualtiesFemale.Sum(cf => cf.DeadCount),
                    UndeclaredQuantitiesMale = henReport.DeadMale - henReport.CasualtiesMale.Sum(cf => cf.DeadCount)
                };
                ViewData["CasualtyReasonFemale"] = henReport.CasualtiesFemale.Where(cf => cf.DeadCount > 0).Select(cf => new SelectListItem($"{cf.CasualtyReason.Name}: {cf.DeadCount}", cf.CasualtyReasonId.ToString(), true)).ToList();
                ViewData["CasualtyReasonMale"] = henReport.CasualtiesMale.Where(cf => cf.DeadCount > 0).Select(cm => new SelectListItem($"{cm.CasualtyReason.Name}: {cm.DeadCount}", cm.CasualtyReasonId.ToString(), true)).ToList();

                model.UndeclaredQuantitiesFemaleDepopulation = henReport.DepopulateFemale - henReport.Depopulations.Where(d => d.IsFemale == true).Sum(ds => ds.DepopulationCount);
                model.UndeclaredQuantitiesMaleDepopulation = henReport.DepopulateMale - henReport.Depopulations.Where(d => d.IsFemale == false).Sum(ds => ds.DepopulationCount);
                ViewData["DepopulationsReasonFemale"] = henReport.Depopulations.Where(d => d.IsFemale && d.DepopulationCount > 0).Select(df => new SelectListItem($"{df.DepopulationReason.Name}: {df.DepopulationCount}", df.DepopulationReasonId.ToString(), true)).ToList();
                ViewData["DepopulationsReasonMale"] = henReport.Depopulations.Where(d => !d.IsFemale && d.DepopulationCount > 0).Select(dm => new SelectListItem($"{dm.DepopulationReason.Name}: {dm.DepopulationCount}", dm.DepopulationReasonId.ToString(), true)).ToList();


                // Return view with henReport info
                return View("Details", model);
            }
            catch (NotFoundException)
            {
                return NotFound();
            }
        }
        #endregion

        #region Delete
        public async Task<JsonResult> Delete(string id)
        {
            string message = "";
            try
            {
                // Get henReport from database
                HenReport henReport = this.henReportService.GetAll().Where(hr => hr.Id == new Guid(id)).Include(hr => hr.HenBatch).FirstOrDefault();

                if (!CheckAuthorization(henReport.HenBatch.HenStage))
                    throw new UserException(Lang.UnauthorizedError);

                bool createAndApprove = operationContext.UserIsInAnyRole(Roles.HenReportLayingAdjustmentApprover, Roles.HenReportBreedingAdjustmentApprover, Roles.BackofficeSuperAdministrator)
                    || henReportBusinessLogic.LastReport(henReport.Id, henReport.HenBatchId);

                await this.henReportBusinessLogic.CreateRequestToDelete(henReport, createAndApprove);
                this.HttpContext.Response.StatusCode = (int)HttpStatusCode.OK;
                message = createAndApprove ?
                    localizer[Lang.DeleteSuccess]
                    : localizer[Lang.RequestSuccess];
            }
            catch (NotFoundException ex)
            {
                this.HttpContext.Response.StatusCode = (int)HttpStatusCode.NotFound;
                message = ex.Message;
            }
            catch (ValidationException ex)
            {
                this.HttpContext.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                foreach (KeyValuePair<string, string> error in ex.Errors)
                    message += error.Value;
            }
            catch (UserException ex)
            {
                this.HttpContext.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                message = ex.Message;
            }
            catch (Exception)
            {
                this.HttpContext.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                message = localizer[Lang.DeleteUnexpectedError];
            }


            return Json(new
            {
                message
            });
        }
        #endregion

        #region Index Lists
        private void InitLists(HenStage? henStage, Guid? henBatchId = null)
        {
            IQueryable<HenBatch> batches = henBatchService.GetAll(asNoTracking: true)
                .Include(hb => hb.Line).ThenInclude(l => l.Warehouse).ThenInclude(w => w.Cluster);

            if (henStage.HasValue)
                batches = batches.Where(b => b.HenStage == henStage);

            List<SelectListItem> farms = batches.Select(b => b.Farm)
                .OrderBy(f => f.Name)
                .Select(f => new SelectListItem(f.Name, f.Id.ToString())).Distinct()
                .ToList();

            ViewData["Farms"] = farms;

            if (farms.Count != 0)
            {
                ViewData["Genetics"] = henBatchBusinessLogic.GetGenetics(henStage, selectedFarm: Guid.Parse(farms[0].Value));
            }
            else
            {
                ViewData["Genetics"] = new List<SelectListItem>();
            }

            ViewData["Clusters"] = batches.Select(b => b.Line.Warehouse.Cluster)
                .Distinct()
                .OrderBy(c => c.Name)
                .Select(c => new SelectListItem(c.Name, c.Id.ToString()))
                .ToList(); ;

            ViewData["Warehouses"] = batches.Select(b => b.Line.Warehouse)
                .Distinct()
                .OrderBy(w => w.Name)
                .Select(w => new SelectListItem(w.Name, w.Id.ToString()))
                .ToList();

            ViewData["Lines"] = batches.Select(b => b.Line)
                .Distinct()
                .OrderBy(l => l.Name)
                .Select(l => new SelectListItem(l.Name, l.Id.ToString()))
                .ToList();

            List<SelectListItem> henBatches = batches.Select(c => new SelectListItem(c.ToString(), c.Id.ToString()))
                .ToList().OrderBy(h => h.Text).ToList();

            ViewData["HenBatches"] = henBatches;

            if (henBatchId != null)
            {
                int index = henBatches.FindIndex(hb => hb.Value.Equals(henBatchId.ToString(), StringComparison.InvariantCultureIgnoreCase));
                henBatches[index].Selected = true;
                HenBatch a = this.henBatchService.GetFull(new Guid(henBatches[index].Value));
                ViewData["SelectedHenBatch"] = henBatches[index].Value;
                ViewData["SelectedLine"] = a.LineId.ToString();
                ViewData["SelectedWarehouse"] = a.Line.WarehouseId.ToString();
                ViewData["SelectedCluster"] = a.Line.Warehouse.ClusterId.ToString();
                ViewData["SelectedFarm"] = a.FarmId.HasValue ? a.FarmId.Value.ToString() : string.Empty;
                ViewData["SelectedGenetic"] = a.GeneticId.ToString();
            }

            ViewData["HenStages"] = GetHenStages();
        }
        #endregion

        #region Establish Death Quantities Reasons
        public IActionResult EstablishDeathAndDepopulationQuantitiesReasons(HenStage? henStage, Guid? henReportId = null, List<string> henReportsId = null, string message = null, bool fromCreation = false)
        {
            ViewData["Title"] = localizer[Lang.EstablishDeathAndDepopulationQuantitiesReasonsTitle];
            ViewData["HenStage"] = henStage;
            ViewData["Messages"] = JsLocalizer.GetLocalizedResources(JsLang.EstablishQuantitiesReasons, localizer);
            ViewData["FromCreation"] = fromCreation;
            if (!string.IsNullOrEmpty(message))
                ViewData["Message"] = message;

            List<Guid> henReports = henReportsId != null && henReportsId.Any() ?
                henReportsId.Count() == 1 ? henReportsId.First().Split(",").Select(s => Guid.Parse(s)).ToList() : henReportsId.Select(s => Guid.Parse(s)).ToList()
                : new List<Guid>();
            Guid currentHenReport = henReports.Count() == 0 && henReportId.HasValue ? henReportId.Value : henReports.First();

            HenReport henReport = henReportService.GetWithHenBatch(currentHenReport);
            Guid? nextHenReport = henReports.Count() > 1 ? henReports[1] : (Guid?)null;
            ViewData["NextHenReport"] = nextHenReport.HasValue ? henReportService.GetAll()
                .Include(hr => hr.HenBatch).ThenInclude(hb => hb.Line).FirstOrDefault(hr => hr.Id == nextHenReport.Value).HenBatch.Line.Name : "";
            ViewData["HenReportId"] = currentHenReport;
            ViewData["HenReports"] = henReports;
            ViewData["SubTitles"] =
                (deadSubtitle: string.Format(localizer[Lang.EstablishDeathQuantitiesReasonsSubTitle], henReport.DeadFemale.ToString(), henReport.DeadMale.ToString()),
                 depopulateSubtitle: string.Format(localizer[Lang.EstablishDepopulationQuantitiesReasonsSubTitle], henReport.DepopulateFemale.ToString(), henReport.DepopulateMale.ToString()));
            ViewData["Quantities"] = (deadFemale: henReport.DeadFemale, deadMale: henReport.DeadMale, depopulateFemale: henReport.DepopulateFemale, depopulateMale: henReport.DepopulateMale);
            IQueryable<TenantConfiguration> configs = tenantConfigurationService.GetAll().Where(c => c.TenantId == operationContext.GetUserTenantId());

            ViewData["HideDepopulate"] = configs.Any(s => (s.TenantConfigurationEnum == TenantConfigurationEnum.DepopulateBreeding && s.Value == "True" && henStage == HenStage.Breeding)
                                                           || (s.TenantConfigurationEnum == TenantConfigurationEnum.DepopulateLaying && s.Value == "True" && henStage == HenStage.Laying));

            return View(new EstablishDeathQuantitiesReasonsDTO()
            {
                Date = henReport.Date.ToString("dd/MM/yyyy"),
                Farm = henReport.HenBatch.Farm.ToString(),
                HenBatch = $"{henReport.HenBatch.Line.Warehouse.Name} | {henReport.HenBatch.Line.Name} | {henReport.HenBatch.Code}"
            });
        }

        [HttpGet]
        public IActionResult GetDeathAndDepopulationTables(Guid henReportId, HenStage henStage)
        {
            (List<string> headers, List<Dictionary<string, string>> table) = henReportBusinessLogic.GetDeathQuantitiesTable(henReportId, henStage);
            (List<string> headers, List<Dictionary<string, string>> table) depopulate = henReportBusinessLogic.GetDepopulateTable(henReportId, henStage);

            HenReport henReport = henReportService.GetAll().Where(hr => hr.Id == henReportId).FirstOrDefault();
            bool hasDepopulateValues = henReport.DepopulateFemale != 0 || henReport.DepopulateMale != 0;
            bool hasDeathValues = henReport.DeadFemale != 0 || henReport.DeadMale != 0;

            if (table.Count == 0 && depopulate.table.Count == 0 && hasDepopulateValues && hasDeathValues)
                return new JsonResult(new
                {
                    exceptionManager.Handle(new UserException(this.localizer[Lang.ReasonsDeathDepopulateNotFound])).Message
                });
            else if (table.Count == 0 && hasDeathValues)
                return new JsonResult(new
                {
                    exceptionManager.Handle(new UserException(this.localizer[Lang.ReasonsDeathNotFound])).Message
                });
            else if (depopulate.table.Count == 0 && hasDepopulateValues)
                return new JsonResult(new
                {
                    exceptionManager.Handle(new UserException(this.localizer[Lang.ReasonsDepopulateNotFound])).Message
                });

            return Ok(new
            {
                dead = new { headers, table },
                depopulate = new { depopulate.headers, depopulate.table }
            });
        }

        [HttpGet]
        public IActionResult GetDeathTable(Guid henReportId, HenStage henStage)
        {
            (List<string> headers, List<Dictionary<string, string>> table) = henReportBusinessLogic.GetDeathQuantitiesTable(henReportId, henStage);

            HenReport henReport = henReportService.GetAll().Where(hr => hr.Id == henReportId).FirstOrDefault();
            bool hasDeathValues = henReport.DeadFemale != 0 || henReport.DeadMale != 0;

            if (table.Count == 0 && hasDeathValues)
                return new JsonResult(new
                {
                    exceptionManager.Handle(new UserException(this.localizer[Lang.ReasonsDeathNotFound])).Message
                });

            return Ok(new
            {
                dead = new { headers, table }
            });
        }

        [HttpPost]
        public async Task<IActionResult> EditDeathAndDepopulationQuantities(DeathAndDepopulationQuantitiesDTO deathAndDepopulationQuantitiesDTO)
        {
            string message;
            try
            {
                await henReportBusinessLogic.EditDeathAndDepopulationQuantities(deathAndDepopulationQuantitiesDTO);
                message = localizer[Lang.DeathQuantitiesEditSuccess];
            }
            catch (Exception ex)
            {
                this.HttpContext.Response.StatusCode = 500;
                message = ex.Message;
            }
            return new JsonResult(new { message });
        }
        #endregion

        #region Create Methods
        public void InitCapacityUnitLists(HenStage henStage, bool selectKgByDefault = true)
        {
            ViewData["Distances"] = capacityUnitService.GetByMeasurementUnitType(MeasurementUnitType.Distance).OrderBy(t => t.RelativeValue).Select(cu => new SelectListItem(cu.Symbol, cu.Id.ToString(), cu.RelativeValue == 1)).ToList();
            ViewData["LiquidVolumes"] = new List<SelectListItem>() { new SelectListItem(capacityUnitService.Get(CapacityUnits.Litres).Symbol, CapacityUnits.Litres.ToString(), true) };
            ViewData["Units"] = capacityUnitService.GetByMeasurementUnitType(MeasurementUnitType.Unit).OrderBy(t => t.RelativeValue).Select(cu => new SelectListItem(cu.Symbol, cu.Id.ToString(), cu.RelativeValue == 1)).ToList();
            ViewData["Volumes"] = capacityUnitService.GetByMeasurementUnitType(MeasurementUnitType.Volume).OrderBy(t => t.RelativeValue).Select(cu => new SelectListItem(cu.Symbol, cu.Id.ToString(), cu.RelativeValue == 1)).ToList();
            ViewData["Weights"] = ListWithKilogramsAndGAD(selectKgByDefault);
            ViewData["Temperatures"] = capacityUnitService.GetByMeasurementUnitType(MeasurementUnitType.Temperature).OrderBy(t => t.RelativeValue).Select(cu => new SelectListItem(cu.Symbol, cu.Id.ToString(), cu.RelativeValue == 1)).ToList();
            if (henStage == HenStage.Laying) { ViewData["Eggs"] = capacityUnitService.GetByMeasurementUnitType(MeasurementUnitType.Eggs).OrderBy(t => t.RelativeValue).Select(cu => new SelectListItem(cu.Symbol, cu.Id.ToString(), cu.RelativeValue == 1)).ToList(); }
        }

        public string InitCreateLists(HenBatchFilterDTO data)
        {
            List<List<SelectListItem>> containers = henReportBusinessLogic.GetContainers(data);
            ViewData["Farms"] = containers[0];
            ViewData["Clusters"] = containers[1];
            ViewData["Warehouses"] = containers[2];
            ViewData["Lines"] = containers[3];

            ViewData["FemaleOrigins"] = data.CurrentBatchId.HasValue
            ? GetFeedIntakeOrigins(data.CurrentBatchId.Value, data.HenStage, true, data.SelectedFeedIntakeOrigin)
            : new List<SelectListItem>();
            ViewData["MaleOrigins"] = data.CurrentBatchId.HasValue && data.HenStage == HenStage.Laying
            ? GetFeedIntakeOrigins(data.CurrentBatchId.Value, data.HenStage, false, data.SelectedFeedIntakeOrigin)
            : new List<SelectListItem>();
            ViewData["Origins"] = data.CurrentBatchId.HasValue && data.HenStage != HenStage.Laying
            ? GetFeedIntakeOrigins(data.CurrentBatchId.Value, data.HenStage, false, data.SelectedFeedIntakeOrigin)
            : new List<SelectListItem>();

            if (containers[3].Any())
                return "0";
            else // there are no available lines to create a hen report
                return "1";
        }

        #endregion

        #region Create Lists
        private List<SelectListItem> GetHenStages()
        {
            List<SelectListItem> henStages = new List<SelectListItem>();
            foreach (HenStage henStage in EnumUtil.GetValues<HenStage>())
            {
                string name = EnumHelper<HenStage>.GetDisplayName(henStage, localizer);
                string id = ((int)henStage).ToString();
                henStages.Add(new SelectListItem(name, id));
            }
            return henStages.OrderBy(hs => hs.Text).ToList();
        }

        /// <summary>
        /// Gets all clusters with active henbatches.
        /// </summary>
        public List<SelectListItem> GetClusters(HenBatchFilterDTO data)
        {
            if (!CheckAuthorization(data.HenStage))
                return new List<SelectListItem>();
            return henReportBusinessLogic.GetClusters(data);
        }


        /// <summary>
        /// Gets hen warehouses with active henbatches.
        /// </summary>
        public List<SelectListItem> GetWarehouses(HenBatchFilterDTO data)
        {
            if (!CheckAuthorization(data.HenStage))
                return new List<SelectListItem>();
            return henReportBusinessLogic.GetWareHouses(data);
        }

        /// <summary>
        /// Get all lines with active henbatches.
        /// </summary>
        public List<SelectListItem> GetLines(HenBatchFilterDTO data)
        {
            if (!CheckAuthorization(data.HenStage))
                return new List<SelectListItem>();
            return henReportBusinessLogic.GetContainers(data)[3];
        }

        public List<HenReportHenBatchDTO> GetActiveHenBatchesByFarm(Guid farmId, HenStage henStage)
        {
            return henReportBusinessLogic.GetActiveHenBatchesByFarm(farmId, henStage);
        }

        public List<HenReportWarehouseDTO> GetWarehousesWithLinesByBatch(Guid henBatchId)
        {
            return henReportBusinessLogic.GetWarehousesWithLinesByBatch(henBatchId);
        }

        public IActionResult GetPlannedGADByBatch(Guid henBatchId, string date)
        {
            try
            {
                if (henBatchId == Guid.Empty || date == null)
                {
                    throw new ValidationException("Lote e data são obrigatórios");
                }
                CultureInfo culture = CultureInfo.CurrentUICulture;
                DateTime reportDate = Convert.ToDateTime(date, culture);

                // henReportBusinessLogic.ValidateDate(reportDate, henBatchId);

                return Ok(henReportBusinessLogic.GetPlannedGADByBatch(henBatchId, reportDate));
            }
            catch (ValidationException e)
            {
                return BadRequest(new { e.Message });
            }
        }

        /// <summary>
        /// Get all possible origins for feed intake.
        /// </summary>
        public List<SelectListItem> GetFeedIntakeOrigins(Guid henBatchId, HenStage? henStage, bool isFemale = false, Guid? selected = null)
        {
            IEnumerable<Guid> formulasConsumed = henBatchService.GetAll().Where(hb => hb.Id == henBatchId).SelectMany(hb => hb.FormulasConsumed).Select(fc => fc.FormulaId);

            IOrderedQueryable<Container> origins = henReportBusinessLogic
                                                        .GetFeedIntakeOrigins(henBatchId)
                                                        .OrderBy(o => o.DetailedName);
            int originCodeFilterCount = 0;
            if (selected == null && henStage == HenStage.Laying)
            {
                string startsWithString = isFemale ? "F" : "M";
                originCodeFilterCount = origins.Count(o => o.Code.StartsWith(startsWithString));

                if (originCodeFilterCount == 1)
                    selected = origins.Where(o => o.Code.StartsWith(startsWithString)).Select(o => o.Id).FirstOrDefault();
            }

            return origins.Select(o => new SelectListItem(o.DetailedName, o.Id.ToString(), selected.HasValue && o.Id == selected))
                          .ToList();
        }

        public List<SelectListItem> GetAvailableMaterialBatches(Guid henBatchId, Guid originId)
        {
            return henReportBusinessLogic.GetAvailableMaterialBatches(henBatchId, originId)
                .OrderBy(mb => mb.MaterialBatch.CreatedDate)
                .Select(mb => new SelectListItem($"{mb.MaterialBatch.Name} | {decimal.Round(mb.Quantity, 2)} | {mb.MaterialBatch.ProductionDate}", mb.MaterialBatchId.ToString()))
                .ToList();
        }

        public bool CheckIfProduces(Guid originId)
        {
            return henReportBusinessLogic.CheckIfProduces(originId);
        }

        public List<HenReportEggsDTO> GetEggsMaterials(List<string> ids = null)
        {
            List<HenReportEggsDTO> materials = new List<HenReportEggsDTO>();
            IQueryable<Material> materialEggs = materialService.GetAllWithMaterialType()
                .Where(m => m.Active && m.MaterialType.Path.Contains(MaterialTypePaths.InsumoMateriaPrimaHuevosHuevoSinClasificar))
                .OrderBy(m => m.InternalId);

            IQueryable<Material> comercialEggs = materialEggs.Where(m => m.MaterialType.Path.Contains(MaterialTypePaths.InsumoMateriaPrimaHuevosHuevoSinClasificarComercialPreclasificado));
            IQueryable<Material> hatchableEggs = materialEggs.Where(m => m.MaterialType.Path.Contains(MaterialTypePaths.InsumoMateriaPrimaHuevosHuevoSinClasificarIncubablePreclasificado));

            if (ids == null || !ids.Any())
            {
                if (!comercialEggs.Any() && !hatchableEggs.Any())
                    ModelState.AddModelError("EggsValidationErrors", string.Format(localizer[Lang.NoEggs], materialTypeService.Get(MaterialTypes.InsumoMateriaPrimaHuevosHuevoSinClasificar).DetailedName));
                else if (!hatchableEggs.Any())
                    ModelState.AddModelError("EggsValidationErrors", string.Format(localizer[Lang.NoEggs], materialTypeService.Get(MaterialTypes.InsumoMateriaPrimaHuevosHuevoSinClasificarIncubablePreclasificado).DetailedName));
                else if (!comercialEggs.Any())
                    ModelState.AddModelError("EggsValidationErrors", string.Format(localizer[Lang.NoEggs], materialTypeService.Get(MaterialTypes.InsumoMateriaPrimaHuevosHuevoSinClasificarComercialPreclasificado).DetailedName));
            }
            else
            {
                hatchableEggs = hatchableEggs.Where(he => !ids.Any(i => i == he.Id.ToString()));
                comercialEggs = comercialEggs.Where(ce => !ids.Any(i => i == ce.Id.ToString()));
            }

            if (hatchableEggs.Any())
                materials.AddRange(hatchableEggs.Select(c => new HenReportEggsDTO(c)));
            if (comercialEggs.Any())
                materials.AddRange(comercialEggs.Select(c => new HenReportEggsDTO(c)));

            return materials;
        }

        /// <summary>
        /// Gets the henbatch and its genetic from a line.
        /// </summary>
        public async Task<IActionResult> GetHenbatch(Guid lineId)
        {
            HenBatch henbatch = await henBatchService.GetAllActive()
                .Where(b => b.LineId == lineId)
                .FirstOrDefaultAsync();

            if (henbatch != null)
            {
                if (!CheckAuthorization(henbatch.HenStage))
                    return Json(new { localizer[Lang.HandleUnauthorizedEx].Value });

                return Json(new
                {
                    assignedFormulas = GetFormulas(henbatch),
                    henbatchId = henbatch.Id,
                    geneticName = henbatch.Genetic.Name,
                    henAmountFemale = henbatch.HenAmountFemale,
                    henAmountMale = henbatch.HenAmountMale,
                    henbatchName = henbatch.DetailedName,
                    parentId = henbatch.ParentId
                });
            }

            else
            {
                return Json(new { localizer[Lang.HandleNotFounddEx].Value });
            }
        }

        public List<SelectListItem> GetFormulas(HenBatch henBatch)
        {
            return henBatch.FormulasConsumed
                .OrderBy(fc => fc.Formula.Name)
                .Select(fc => new SelectListItem(fc.Formula.Name, fc.FormulaId.ToString())).ToList();
        }

        /// <summary>
        /// Converts all inputs values from hen report to its relative base value.
        /// </summary>
        private void SetTotalValues(HenReportViewModel viewModel)
        {
            HenReportDTO dto = henReportBusinessLogic.GetBaseValues(viewModel.GetHenReportDTO(), viewModel.HenStage);
            viewModel.SetValues(dto.ReturnedValues);
        }

        /// <summary>
        /// Converts all inputs values from warehouse hen report to its relative base value.
        /// </summary>
        private async Task SetTotalValues(WarehouseHenReportViewModel viewModel)
        {
            viewModel.WaterConsumption = GetRelativeValue(viewModel.WaterConsumption, viewModel.WaterConsumptionMeasure);
            viewModel.MinTemp = capacityUnitBusinessLogic.GetBaseRelativeValue(new Guid(viewModel.MinTempMeasure), viewModel.MinTemp.Value);
            viewModel.MaxTemp = capacityUnitBusinessLogic.GetBaseRelativeValue(new Guid(viewModel.MaxTempMeasure), viewModel.MaxTemp.Value);

            foreach (HenBatchHenReportViewModel hbhr in viewModel.LineReports)
            {
                if (hbhr.FeedIntakeMeasure == CapacityUnits.GAD.ToString())
                {
                    int henAmount = await henBatchService.GetAll().Where(hb => hb.Id == Guid.Parse(hbhr.HenBatchId)).Select(hb => hb.HenAmountFemale + hb.HenAmountMale).FirstAsync();
                    hbhr.FeedIntake = capacityUnitBusinessLogic.ConvertValue(CapacityUnits.Grams, CapacityUnits.Kilograms, hbhr.FeedIntake) * henAmount;
                }
                else hbhr.FeedIntake = GetRelativeValue(hbhr.FeedIntake, hbhr.FeedIntakeMeasure);
                if (hbhr.FeedIntakeFemaleMeasure == CapacityUnits.GAD.ToString())
                {
                    int femaleHenAmount = await henBatchService.GetAll().Where(hb => hb.Id == Guid.Parse(hbhr.HenBatchId)).Select(hb => hb.HenAmountFemale).FirstAsync();
                    hbhr.FeedIntakeFemale = capacityUnitBusinessLogic.ConvertValue(CapacityUnits.Grams, CapacityUnits.Kilograms, hbhr.FeedIntakeFemale) * femaleHenAmount;
                }
                else hbhr.FeedIntakeFemale = GetRelativeValue(hbhr.FeedIntakeFemale, hbhr.FeedIntakeFemaleMeasure);
                if (hbhr.FeedIntakeMaleMeasure == CapacityUnits.GAD.ToString())
                {
                    int maleHenAmount = await henBatchService.GetAll().Where(hb => hb.Id == Guid.Parse(hbhr.HenBatchId)).Select(hb => hb.HenAmountMale).FirstAsync();
                    hbhr.FeedIntakeMale = capacityUnitBusinessLogic.ConvertValue(CapacityUnits.Grams, CapacityUnits.Kilograms, hbhr.FeedIntakeMale) * maleHenAmount;
                }
                else hbhr.FeedIntakeMale = GetRelativeValue(hbhr.FeedIntakeMale, hbhr.FeedIntakeMaleMeasure);
                hbhr.DeadFemale = GetRelativeValue(hbhr.DeadFemale, hbhr.DeadFemaleMeasure);
                hbhr.DeadMale = GetRelativeValue(hbhr.DeadMale, hbhr.DeadMaleMeasure);
                hbhr.DepopulateFemale = GetRelativeValue(hbhr.DepopulateFemale, hbhr.DepopulateFemaleMeasure);
                hbhr.DepopulateMale = GetRelativeValue(hbhr.DepopulateMale, hbhr.DepopulateMaleMeasure);
                hbhr.ToCageFemale = GetRelativeValue(hbhr.ToCageFemale, hbhr.ToCageFemaleMeasure);
                hbhr.ToCageMale = GetRelativeValue(hbhr.ToCageMale, hbhr.ToCageMaleMeasure);
                hbhr.ToFloorFemale = GetRelativeValue(hbhr.ToFloorFemale, hbhr.ToFloorFemaleMeasure);
                hbhr.ToFloorMale = GetRelativeValue(hbhr.ToFloorMale, hbhr.ToFloorMaleMeasure);
            }
        }

        private decimal? GetRelativeValue(decimal? quantity, string measure)
        {
            return quantity.HasValue
                ? capacityUnitBusinessLogic.GetBaseRelativeValue(new Guid(measure), quantity.Value)
                : quantity;
        }
        private decimal GetRelativeValue(decimal quantity, string measure)
        {
            return !string.IsNullOrEmpty(measure)
                ? capacityUnitBusinessLogic.GetBaseRelativeValue(new Guid(measure), quantity)
                : quantity;
        }
        private int? GetRelativeValue(int? quantity, string measure)
        {
            return quantity.HasValue
                ? Convert.ToInt32(capacityUnitBusinessLogic.GetBaseRelativeValue(new Guid(measure), quantity.Value))
                : quantity;
        }
        #endregion

        private bool CheckAuthorization(HenStage? henStage)
        {
            if (henStage.HasValue)
            {
                switch (henStage)
                {
                    case HenStage.Laying:
                        if (!this.operationContext.UserIsInAnyRole(
                            Roles.BackofficeSuperAdministrator,
                            Roles.BackofficeLayingAdministrator,
                            Roles.BackofficeLayingUser,
                            Roles.BackofficeLayingDailyReportsAdministrator,
                            Roles.BackofficeLayingDailyReportsUser,
                            Roles.BackofficeLayingHenReportAdministrator,
                            Roles.BackofficeLayingHenReportUser,
                            Roles.BackofficeDailyReportWithoutDateValidation,
                            Roles.HenReportLayingAdjustmentApprover))
                            return false;
                        break;

                    case HenStage.Breeding:
                        if (!this.operationContext.UserIsInAnyRole(
                            Roles.BackofficeSuperAdministrator,
                            Roles.BackofficeBreedingAdministrator,
                            Roles.BackofficeBreedingUser,
                            Roles.BackofficeBreedingDailyReportsAdministrator,
                            Roles.BackofficeBreedingDailyReportsUser,
                            Roles.BackofficeBreedingHenReportAdministrator,
                            Roles.BackofficeBreedingHenReportUser,
                            Roles.BackofficeDailyReportWithoutDateValidation,
                            Roles.HenReportBreedingAdjustmentApprover))
                            return false;
                        break;

                    default:
                        if (!this.operationContext.UserIsInRole(
                            Roles.BackofficeSuperAdministrator))
                            return false;
                        break;
                }
                ;
                return true;
            }
            else
            {
                if (!this.operationContext.UserIsInAnyRole(
                            Roles.BackofficeSuperAdministrator,
                            Roles.BackofficeLayingAdministrator,
                            Roles.BackofficeLayingUser,
                            Roles.BackofficeLayingDailyReportsAdministrator,
                            Roles.BackofficeLayingDailyReportsUser,
                            Roles.BackofficeLayingHenReportAdministrator,
                            Roles.BackofficeLayingHenReportUser,
                            Roles.BackofficeBreedingAdministrator,
                            Roles.BackofficeBreedingUser,
                            Roles.BackofficeBreedingDailyReportsAdministrator,
                            Roles.BackofficeBreedingDailyReportsUser,
                            Roles.BackofficeBreedingHenReportAdministrator,
                            Roles.BackofficeBreedingHenReportUser))
                    return false;
                else
                    return true;
            }
        }

        #region Validations
        [HttpGet]
        public IActionResult RedirectValidations(Guid? henBatchId)
        {
            if (henBatchId.HasValue)
            {
                HenBatch henbatch = henBatchService.GetWithFormulas(henBatchId.Value);

                if (henbatch.HenAmountFemale == 0 && henbatch.HenAmountMale == 0)
                    return NotFound(new { message = this.localizer[Lang.DoesntHaveHenAmount] });
                else if (!henbatch.FormulasConsumed.Any())
                    return NotFound(new { message = this.localizer[Lang.UnassignedFormula] });
                else
                    return Json("");
            }
            else
                return Json("");
        }

        public IActionResult ValidateDeathReasons(Guid henReportId)
        {
            HenReport henReport = henReportService.GetFull(henReportId);
            IQueryable<CasualtyReason> casualtyReasons = casualtyReasonService.GetAll()
                .Where(cr => cr.Active || henReport.Casualties.Select(c => c.CasualtyReasonId).Distinct().Contains(cr.Id));

            if (!casualtyReasons.Any()) return BadRequest(new { invalid = localizer[Lang.NoActiveCasualtyReasons].Value });
            else return Ok();
        }

        private bool CheckDifferences(HenReportRectificationDTO dto, HenStage henStage)
        {
            bool hasDifferences = dto.BrokenEggs == 0 && dto.DeadFemale == 0 && dto.DeadMale == 0 &&
                    dto.DepopulateFemale == 0 && dto.DepopulateMale == 0 && dto.ToCageFemale == 0 && dto.ToCageMale == 0 &&
                    dto.ToFloorFemale == 0 && dto.ToFloorMale == 0 && !dto.WaterPillQuantityChange
                    && !dto.WaterPhChange && !dto.WaterConsumptionChange &&
                    !dto.WaterChlorineConcentrationChange && !dto.EggsDifferences.Any(e => e.Quantity != 0);
            if (henStage == HenStage.Laying)
                return hasDifferences && (Math.Truncate(dto.FeedIntakeFemale) / 100) == 0 && (Math.Truncate(dto.FeedIntakeMale) / 100) == 0;
            else
                return hasDifferences && (Math.Truncate(dto.FeedIntake) / 100) == 0;
        }

        private void Validate(HenReportViewModel viewModel, bool actionEdit = false)
        {
            SetTotalValues(viewModel);

            if (viewModel.FarmId == null ||
                    viewModel.WarehouseId == null ||
                    viewModel.LineId == null)
                ModelState.AddModelError(string.Empty, localizer[Lang.NoContainerSelected]);

            if (viewModel.MinTemp > viewModel.MaxTemp)
                ModelState.AddModelError("MaxTemp", localizer[Lang.MaxTempEx]);

            if (!actionEdit && viewModel.HenBatchId != null && viewModel.DepopulateFemale != null && viewModel.DepopulateFemale > 0)
            {
                //As depopulation doesn't affect the hen amount, it is necessary to confirm there are hen left to depopulate.
                int depopulationDDBB = this.henReportService.GetAll().Where(hr => hr.Id.ToString() == viewModel.HenBatchId).Select(hr => hr.DepopulateFemale).Sum();
                if (viewModel.HenAmountFemale - viewModel.DeadFemale - viewModel.DepopulateFemale - depopulationDDBB < 0)
                    ModelState.AddModelError(string.Empty, localizer[Lang.NoHenEx]);
            }

            if (!actionEdit && viewModel.HenBatchId != null && viewModel.DepopulateMale != null && viewModel.DepopulateMale > 0)
            {
                //As depopulation doesn't affect the hen amount, it is necessary to confirm there are hen left to depopulate.
                int depopulationDDBB = this.henReportService.GetAll().Where(hr => hr.Id.ToString() == viewModel.HenBatchId).Select(hr => hr.DepopulateMale).Sum();
                if (viewModel.HenAmountMale - viewModel.DeadMale - viewModel.DepopulateMale - depopulationDDBB < 0)
                    ModelState.AddModelError(string.Empty, localizer[Lang.NoHenEx]);
            }
            if (actionEdit)
            {
                HenReportRectificationDTO dto = henReportBusinessLogic.GetDifferences(viewModel.MapViewModel(), new Guid(viewModel.Id), viewModel.HenStage);
                if (CheckDifferences(dto, viewModel.HenStage))
                    ModelState.AddModelError(string.Empty, localizer[Lang.NoChanges]);

                if (viewModel.HenBatchId != null && dto.DepopulateMale > 0)
                {
                    //As depopulation doesn't affect the hen amount, it is necessary to confirm there are hen left to depopulate.
                    int depopulationDDBB = this.henReportService.GetAll().Where(hr => hr.Id.ToString() == viewModel.HenBatchId).Select(hr => hr.DepopulateMale).Sum();
                    if (viewModel.HenAmountMale - dto.DeadMale - dto.DepopulateMale - depopulationDDBB < 0)
                        ModelState.AddModelError(string.Empty, localizer[Lang.NoHenEx]);
                }

                if (viewModel.HenBatchId != null && dto.DepopulateFemale > 0)
                {
                    //As depopulation doesn't affect the hen amount, it is necessary to confirm there are hen left to depopulate.
                    int depopulationDDBB = this.henReportService.GetAll().Where(hr => hr.Id.ToString() == viewModel.HenBatchId).Select(hr => hr.DepopulateFemale).Sum();
                    if (dto.HenAmountFemale - dto.DeadFemale - dto.DepopulateFemale - depopulationDDBB < 0)
                        ModelState.AddModelError(string.Empty, localizer[Lang.NoHenEx]);
                }
            }
        }

        private async Task ValidateFromWarehouse(WarehouseHenReportViewModel model)
        {
            await SetTotalValues(model);

            for (int i = 0; i < model.LineReports.Count(); i++)
            {
                if (model.LineReports[i].HenBatchId != null && model.LineReports[i].DepopulateFemale != null && model.LineReports[i].DepopulateFemale > 0)
                {
                    //As depopulation doesn't affect the hen amount, it is necessary to confirm there are hen left to depopulate.
                    int depopulationDDBB = this.henReportService.GetAll().Where(hr => hr.Id.ToString() == model.LineReports[i].HenBatchId).Select(hr => hr.DepopulateFemale).Sum();
                    if (model.LineReports[i].HenAmountFemale - model.LineReports[i].DeadFemale - model.LineReports[i].DepopulateFemale - depopulationDDBB < 0)
                        ModelState.AddModelError("LineReports[" + i + "].HensError", localizer[Lang.NoHenEx]);
                }

                if (model.LineReports[i].HenBatchId != null && model.LineReports[i].DepopulateMale != null && model.LineReports[i].DepopulateMale > 0)
                {
                    //As depopulation doesn't affect the hen amount, it is necessary to confirm there are hen left to depopulate.
                    int depopulationDDBB = this.henReportService.GetAll().Where(hr => hr.Id.ToString() == model.LineReports[i].HenBatchId).Select(hr => hr.DepopulateMale).Sum();
                    if (model.LineReports[i].HenAmountMale - model.LineReports[i].DeadMale - model.LineReports[i].DepopulateMale - depopulationDDBB < 0)
                        ModelState.AddModelError("LineReports[" + i + "].HensError", localizer[Lang.NoHenEx]);
                }
            }

        }

        public IActionResult ValidateDate(string date, Guid henBatchId)
        {
            try
            {
                CultureInfo culture = CultureInfo.CurrentUICulture;
                DateTime reportDate = Convert.ToDateTime(date, culture);
                henReportBusinessLogic.ValidateDate(reportDate, henBatchId);
                return Ok();
            }
            catch (ValidationException e)
            {
                return BadRequest(e.Message);
            }
        }

        [HttpGet]
        public IActionResult ValidateDateSkipHenAmount(string date, Guid henBatchId)
        {
            try
            {
                CultureInfo culture = CultureInfo.CurrentUICulture;
                DateTime reportDate = Convert.ToDateTime(date, culture);

                DateTime yesterday = reportDate.Date.AddDays(-1);
                DateTime? henBatchStart = henBatchService.GetBirdsFirstLoadingDate(henBatchId);

                if (henBatchStart.HasValue && henBatchStart > reportDate)
                {
                    throw new ValidationException(localizer.GetString(Lang.WrongReportDate, henBatchStart.Value.ToShortDateString()));
                }

                // Get the real last report date for this hen batch using the GetRealLastReportDate method
                // This will check for parent HenBatch and all its children
                string lastReportDateStr = (GetRealLastReportDate(henBatchId) as OkObjectResult)?.Value as string;

                // Check if its a valid date string
                DateTime lastReportDate;
                if (string.IsNullOrEmpty(lastReportDateStr) || !DateTime.TryParseExact(lastReportDateStr, "dd/MM/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out lastReportDate))
                {
                    // If parsing fails, use yesterday as fallback
                    lastReportDate = DateTime.Now.AddDays(-1);
                }

                // Get the reports count for additional validation checks
                int reportCount = henReportService.GetAll()
                    .Where(hr => hr.HenBatchId == henBatchId && hr.ReportEnum == ReportEnum.New)
                    .Count();

                // Check if user has special permissions
                bool hasSpecialPermissions = operationContext.UserIsInAnyRole(
                    Roles.BackofficeSuperAdministrator,
                    Roles.BackofficeDailyReportWithoutDateValidation);

                // Check if there are existing reports with egg data
                bool hasReportsWithEggData = henReportService.GetAll()
                    .Where(hr => hr.HenBatchId == henBatchId && hr.ReportEnum == ReportEnum.New)
                    .Any(hr => hr.TotalEggs > 0 || hr.ClassifiedEggs.Any());

                TimeSpan gap = reportDate.Date - lastReportDate.Date;

                // If there are reports with egg data, allow next day; otherwise only allow same date
                if (hasReportsWithEggData)
                {
                    // Allow next day if there are existing reports with egg data
                    if (gap.TotalDays > 1)
                    {
                        string nextExpectedDate = lastReportDate.Date.AddDays(1).ToString("dd/MM/yyyy");
                        string errorMessage = $"Deve primeiro carregar todos os relatórios que faltam desde {nextExpectedDate}";
                        throw new ValidationException(errorMessage);
                    }
                }
                else
                {
                    // Only allow same date if no reports with egg data exist
                    if (gap.TotalDays != 0)
                    {
                        string lastReportDateFormatted = lastReportDate.ToString("dd/MM/yyyy");
                        string errorMessage = $"Só é possível registrar dados para a data do último relatório: {lastReportDateFormatted}";
                        throw new ValidationException(errorMessage);
                    }
                }

                // Always validate date sequence unless user has special permissions
                if (!hasSpecialPermissions)
                {
                    if (reportCount == 0 && henBatchStart.HasValue)
                    {
                        if (reportDate.Date != henBatchStart.Value.Date && reportDate.Date != henBatchStart.Value.Date.AddDays(1))
                        {
                            throw new ValidationException(localizer.GetString(Lang.NoReports, henBatchStart.Value.ToShortDateString()));
                        }
                    }
                    else
                    {
                        // There are existing reports - use the same egg data check from outer scope
                        TimeSpan daysDifference = reportDate.Date - lastReportDate.Date;

                        if (hasReportsWithEggData)
                        {
                            // Allow next day if there are existing reports with egg data
                            if (daysDifference.TotalDays > 1)
                            {
                                string nextExpectedDate = lastReportDate.Date.AddDays(1).ToString("dd/MM/yyyy");
                                string errorMessage = $"Deve primeiro carregar todos os relatórios que faltam desde {nextExpectedDate}";
                                throw new ValidationException(errorMessage);
                            }
                            else if (daysDifference.TotalDays < 0)
                            {
                                string formattedLastReportDate = lastReportDate.ToString("dd/MM/yyyy");
                                string errorMessage = $"Não é possível criar um relatório para uma data anterior ao último relatório ({formattedLastReportDate})";
                                throw new ValidationException(errorMessage);
                            }
                        }
                        else
                        {
                            // Only allow same date if no reports with egg data exist
                            if (daysDifference.TotalDays != 0)
                            {
                                string formattedLastReportDate = lastReportDate.ToString("dd/MM/yyyy");
                                string errorMessage = $"Só é possível registrar dados para a data do último relatório: {formattedLastReportDate}";
                                throw new ValidationException(errorMessage);
                            }
                        }
                    }
                }
                return Ok();
            }
            catch (ValidationException e)
            {
                return BadRequest(e.Message);
            }
            catch (Exception e)
            {
                return BadRequest($"An unexpected error occurred: {e.Message}");
            }
        }

        public IActionResult FirstProductionDateValidation(string date, List<string> henBatchesIds)
        {
            CultureInfo culture = CultureInfo.CurrentUICulture;
            DateTime reportDate = Convert.ToDateTime(date, culture);

            try
            {
                henReportBusinessLogic.ValidateFirstProductionDate(reportDate, null, henBatchesIds[0].Split(",").Where(id => !string.IsNullOrEmpty(id)).Select(hb => Guid.Parse(hb)).ToList());
                return Ok();
            }
            catch (ValidationException e)
            {
                return BadRequest(e.Message);
            }
        }

        [HttpGet]
        public IActionResult GetLastReportDate(Guid henBatchId)
        {
            try
            {
                // Get all reports for this hen batch
                var reports = henReportService.GetAll()
                    .Where(hr => hr.HenBatchId == henBatchId && hr.ReportEnum == ReportEnum.New)
                    .ToList();

                if (reports.Count == 0)
                {
                    // Try to get the hen batch directly
                    var henBatch = henBatchService.GetAll().FirstOrDefault(hb => hb.Id == henBatchId);
                    if (henBatch != null)
                    {
                        // Try to get the hen batch start date
                        DateTime? henBatchStart = henBatchService.GetBirdsFirstLoadingDate(henBatchId);

                        if (henBatchStart.HasValue && henBatchStart.Value != default && henBatchStart.Value.Year > 2000)
                        {
                            // Return the hen batch start date as the reference point
                            return Ok(henBatchStart.Value.ToString("dd/MM/yyyy"));
                        }

                        // Try to use the hen batch creation min date
                        if (henBatch.HenReportCreationMinDate.HasValue &&
                            henBatch.HenReportCreationMinDate.Value != default &&
                            henBatch.HenReportCreationMinDate.Value.Year > 2000)
                        {
                            return Ok(henBatch.HenReportCreationMinDate.Value.ToString("dd/MM/yyyy"));
                        }
                    }

                    // No valid date found - return yesterday's date as a fallback
                    return Ok(DateTime.Now.AddDays(-1).ToString("dd/MM/yyyy"));
                }

                // Get the last report by sorting the reports by date
                var lastReport = reports.OrderByDescending(hr => hr.Date).FirstOrDefault();

                if (lastReport == null)
                {
                    // Return yesterday's date as a fallback
                    return Ok(DateTime.Now.AddDays(-1).ToString("dd/MM/yyyy"));
                }

                if (lastReport.Date == default || lastReport.Date.Year < 2000)
                {
                    // Invalid date - return yesterday's date as a fallback
                    return Ok(DateTime.Now.AddDays(-1).ToString("dd/MM/yyyy"));
                }

                return Ok(lastReport.Date.ToString("dd/MM/yyyy"));
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet]
        public IActionResult GetRealLastReportDate(Guid henBatchId)
        {
            try
            {
                // Get the HenBatch
                var henBatch = henBatchService.GetAll()
                    .Include(hb => hb.Parent)
                    .FirstOrDefault(hb => hb.Id == henBatchId);

                if (henBatch == null)
                {
                    return BadRequest("HenBatch not found");
                }

                bool isParent = !henBatch.ParentId.HasValue;
                Guid parentId = isParent ? henBatchId : henBatch.ParentId.Value;

                // Get all reports for this hen batch and all its children (if it's a parent)
                // or for the parent and all its siblings (if it's a child)
                List<HenReport> allReports = new List<HenReport>();

                // Get all related hen batches (parent and all children)
                var relatedBatchIds = new List<Guid>();

                // Add the parent batch ID
                relatedBatchIds.Add(parentId);

                // Add all child batch IDs
                var childBatches = henBatchService.GetChildren(parentId).ToList();
                relatedBatchIds.AddRange(childBatches.Select(hb => hb.Id));

                // Get reports for all related batches in a single query
                allReports = henReportService.GetAll()
                    .Where(hr => relatedBatchIds.Contains(hr.HenBatchId) && hr.ReportEnum == ReportEnum.New)
                    .ToList();

                if (allReports.Count == 0)
                {
                    // No reports found for this hen batch or its related batches
                    // Try to get the hen batch start date
                    DateTime? henBatchStart = henBatchService.GetBirdsFirstLoadingDate(parentId);

                    if (henBatchStart.HasValue && henBatchStart.Value != default && henBatchStart.Value.Year > 2000)
                    {
                        // Return the hen batch start date as the reference point
                        return Ok(henBatchStart.Value.ToString("dd/MM/yyyy"));
                    }

                    // Try to use the hen batch creation min date
                    var batchToCheck = henBatchService.GetAll().FirstOrDefault(hb => hb.Id == parentId);
                    if (batchToCheck != null && batchToCheck.HenReportCreationMinDate.HasValue &&
                        batchToCheck.HenReportCreationMinDate.Value != default &&
                        batchToCheck.HenReportCreationMinDate.Value.Year > 2000)
                    {
                        return Ok(batchToCheck.HenReportCreationMinDate.Value.ToString("dd/MM/yyyy"));
                    }
                    return Ok(DateTime.Now.AddDays(-1).ToString("dd/MM/yyyy"));
                }

                // Get the last report by sorting all reports by date
                var lastReport = allReports.OrderByDescending(hr => hr.Date).FirstOrDefault();

                if (lastReport == null)
                {
                    return Ok(DateTime.Now.AddDays(-1).ToString("dd/MM/yyyy"));
                }

                if (lastReport.Date == default || lastReport.Date.Year < 2000)
                {
                    return Ok(DateTime.Now.AddDays(-1).ToString("dd/MM/yyyy"));
                }
                return Ok(lastReport.Date.ToString("dd/MM/yyyy"));
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        private string ValidateHenBatch(HenBatch henBatch, string date)
        {
            List<string> errors = new List<string>();

            // validate formula consumed
            if (!henBatch.FormulasConsumed.Any())
                errors.Add(this.localizer[Lang.UnassignedFormula]);

            // feed intake origins
            IEnumerable<Guid> formulasConsumed = henBatch.FormulasConsumed.Select(fc => fc.FormulaId);

            if (!henBatch.OriginContainers.Any(o => o.Origin.Active && (o.Origin.MaterialContainers.Any(mc => formulasConsumed.Contains(mc.MaterialId) && mc.Quantity > 0) ||
                       o.Origin.AcceptedMaterialType.Any(amt => MaterialTypePaths.InsumoMateriaPrimaAlimentacionFormula.Contains(amt.MaterialType.Path) && (amt.ActionEnum == ActionsEnum.Produce || amt.ActionEnum == ActionsEnum.ConsumeAndProduce)))))
                errors.Add(this.localizer[Lang.NoOrigins]);

            // validate hen amount
            if (henBatch.HenAmountFemale == 0 && henBatch.HenAmountMale == 0)
                errors.Add(this.localizer[Lang.DoesntHaveHenAmount]);

            // validate date
            CultureInfo culture = CultureInfo.CurrentUICulture;
            DateTime reportDate = Convert.ToDateTime(date, culture);
            DateTime yesterday = reportDate.Date.AddDays(-1);

            if (!henBatch.HenReportCreationMinDate.HasValue)
                errors.Add(this.localizer[Lang.DoesntHaveDateStart]);

            // Report date should be greater than hen batch start
            else if (henBatch.HenReportCreationMinDate?.Date > reportDate.Date)
                errors.Add($"{localizer[Lang.WrongReportDate].Value} {henBatch.HenReportCreationMinDate.Value.ToString("d", culture)}).");

            else if (henBatch.HenReportCreationMinDate != yesterday && henBatch.HenReportCreationMinDate?.Date != reportDate.Date && !operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator, Roles.BackofficeDailyReportWithoutDateValidation))
            {
                IQueryable<HenReport> reports = henReportService.GetAll()
                    .Where(hr => hr.HenBatchId == henBatch.Id && hr.ReportEnum == ReportEnum.New);

                DateTime lastReportDate = henReportService.GetAll()
                       .Where(hr => hr.HenBatchId == henBatch.Id && hr.ReportEnum == ReportEnum.New)
                       .OrderByDescending(hr => hr.Date)
                       .Select(hr => hr.Date)
                       .FirstOrDefault();

                if (reports.Count() == 0 && !henBatch.AllowBeginReportsOnAnyDate)
                    errors.Add(String.Format(localizer[Lang.NoReports].Value, henBatch.HenReportCreationMinDate.Value.ToString("d", culture)));

                else if (reports.Count() != 0 && lastReportDate.AddDays(1).Date < reportDate.Date)
                    errors.Add(localizer[Lang.MissingReports] + ' ' + lastReportDate.Date.AddDays(1).ToString("d", culture));

                else if (reports.Count() != 0 && lastReportDate != default && lastReportDate > reportDate)
                    errors.Add($"{localizer[Lang.LastReportDateError].Value} {lastReportDate.ToString("d", culture)}");
            }

            if (errors.Any())
            {
                string message = string.Format(localizer[Lang.ErrorMessage], henBatch.Code + "|" + henBatch.Line.Warehouse.Name + "|" + henBatch.Line.Name) + "<ul>";
                foreach (string e in errors)
                    message = message + "<li>" + e + "</li>";
                return message + "</ul>";
            }
            else
                return "";
        }
        #endregion

        //Get New Row for Materials DTOS
        [HttpGet]
        public IActionResult GetEggsDestination(string lineId)
        {
            if (!containerService.GetAll().Include(c => c.OriginContainers).Any(c => c.OriginContainers.Any(oc => oc.OriginId.ToString() == lineId)))
            {
                this.HttpContext.Response.StatusCode = (int)HttpStatusCode.NotFound;
                return new JsonResult(new
                {
                    exceptionManager.Handle(new UserException(this.localizer[Lang.NoDestination])).Message
                });
            }
            else
                return new JsonResult(new
                {
                    success = true
                });
        }

        public string GetHenStageString(HenStage? henStage)
        {
            if (henStage.HasValue)
                return henStage switch
                {
                    HenStage.Breeding => localizer[Lang.Breeding],
                    _ => localizer[Lang.Laying],
                };
            else
                return "";
        }

        public HenReportDeviationDTO CreateDeviationDTO(string henBatchId)
        {
            //Check if there is any completed hen report for the previous day of the selected date.
            IQueryable<HenReport> reports = henReportService.GetAll()
                        .Where(hr => hr.HenBatchId.ToString() == henBatchId && hr.ReportEnum == ReportEnum.New);
            if (reports.Count() == 0)
                return new HenReportDeviationDTO(henBatchId);
            else
            {
                HenReport previousReport = reports
                            .OrderByDescending(hr => hr.Date)
                            .FirstOrDefault();
                return new HenReportDeviationDTO(henBatchId, previousReport);
            }
        }

        public List<HenReportDeviationDTO> CreateDeviationDTOs(string ids)
        {
            List<HenReportDeviationDTO> dtos = new List<HenReportDeviationDTO>();

            if (string.IsNullOrEmpty(ids))
                return dtos;

            foreach (string id in ids.Split(","))
            {
                if (!string.IsNullOrEmpty(id.Trim()))
                {
                    dtos.Add(CreateDeviationDTO(id));
                }
            }

            return dtos;
        }

        public IActionResult GetBaseRelativeValue(string value, Guid? measureId)
        {
            if (decimal.TryParse(value, out decimal number))
            {
                if (measureId.HasValue)
                {
                    decimal relativeValue = capacityUnitBusinessLogic.GetBaseRelativeValue(measureId.Value, number);
                    return Ok(new { RelativeValue = relativeValue });
                }
                else
                {
                    return Ok(new { RelativeValue = value });
                }
            }
            return Ok();
        }

        public IActionResult ConvertValueToKilogram(string value, Guid measureIdFrom, Guid measureIdTo)
        {
            if (decimal.TryParse(value, out decimal number))
            {
                decimal relativeValue = capacityUnitBusinessLogic.ConvertValue(measureIdFrom, measureIdTo, number);
                return Ok(new { RelativeValue = relativeValue });
            }
            return Ok();
        }

        public Dictionary<Guid, string> GetCasualties(HenStage henStage)
        {
            AreaEnum area = EnumHelper<AreaEnum>.Parse($"{(int)henStage}");
            Dictionary<Guid, string> casualties = casualtyReasonService.GetAll().Where(cr => cr.Area == area).ToDictionary(cr => cr.Id, cr => cr.Name);
            return casualties;
        }

        public Dictionary<Guid, string> GetClassifiedEggs()
        {
            return materialService.GetAllWithMaterialType()
                .Where(m => m.Active && m.MaterialType.Path.Contains(MaterialTypePaths.InsumoMateriaPrimaHuevosHuevoSinClasificar))
                .ToDictionary(m => m.Id, m => m.Name);
        }

        /// <summary>
        /// Exports a filtered list of holidays into an excel file.
        /// Uses the same filter as DataTable.
        /// </summary>
        public async Task<FileResult> ExcelExport(string searchTerm, Dictionary<string, string> data, HenStage? henStage = null)
        {
            ExportResult exportResult = await henReportBusinessLogic.ExportExcel(data, searchTerm, henStage);

            return File(exportResult.Stream, exportResult.ExportMimeType, exportResult.Filename);
        }

        private List<SelectListItem> ListWithKilogramsAndGAD(bool selectKgByDefault) => new List<SelectListItem>() {
            new SelectListItem(capacityUnitService.Get(CapacityUnits.Kilograms).Symbol, CapacityUnits.Kilograms.ToString(), selectKgByDefault),
            new SelectListItem("GAD",CapacityUnits.GAD.ToString(), !selectKgByDefault) };

        public JsonResult BirdMovementValidation(Guid warehouseId, string dateHR)
        {
            DateTime.TryParse(dateHR, out DateTime date);
            List<HenBatch> henBatches = henBatchService.GetAllFromWarehouse(warehouseId).ToList();
            List<string> data = new List<string>();

            foreach (HenBatch hb in henBatches)
            {
                DateTime? henBatchLast = henBatchService.GetBirdsLastLoadingDate(hb.Id);
                if (henBatchLast > date)
                    data.Add(hb.Line.Name);
            }
            data.Sort();
            return new JsonResult(new { data });
        }
        public JsonResult BirdMovementValidationFromHenBatch(Guid henBatchId, string dateHR)
        {
            DateTime.TryParse(dateHR, out DateTime date);
            string data = "";
            DateTime? henBatchLast = henBatchService.GetBirdsLastLoadingDate(henBatchId);
            if (henBatchLast > date)
            {
                HenBatch henBatch = henBatchService.GetAllWithWarehouse(henBatchId);
                data = henBatch.Code + " | " + henBatch.Line.Warehouse.Name + " | " + henBatch.Line.Name;
            }

            return new JsonResult(data);
        }
    }
}