using System;
using System.Collections.Specialized;
using Binit.Framework.Constants.DAL;
using Binit.Framework.Helpers.Configuration;
using Microsoft.Extensions.Configuration;
using Quartz;
using Quartz.Impl;

namespace Binit.Framework.JobScheduler
{
    /// <summary>
    /// Quartz scheduler manager.
    /// </summary>
    public static class QuartzManager
    {
        public static IScheduler CreateScheduler(IConfiguration configuration)
        {
            var dbConfig = new DatabaseConfiguration().Bind(configuration);

            NameValueCollection props = new NameValueCollection
            {
                { "quartz.serializer.type", "json" },
                { "quartz.jobStore.type", "Quartz.Impl.AdoJobStore.JobStoreTX, Quartz" },
                { "quartz.jobStore.dataSource", "default" },
                { "quartz.dataSource.default.connectionString", dbConfig.LogConnection },
                { "quartz.plugin.timeZoneConverter.type", "Quartz.Plugin.TimeZoneConverter.TimeZoneConverterPlugin, Quartz.Plugins.TimeZoneConverter" }
            };

            // Setup the DataSource and JobStore configurations according to the Log Provider.
            switch (dbConfig.LogProvider)
            {
                case DatabaseProviders.SqlServer:
                    props.Add(new NameValueCollection{
                        { "quartz.dataSource.default.provider", "SqlServer" },
                        { "quartz.jobStore.driverDelegateType", "Quartz.Impl.AdoJobStore.SqlServerDelegate, Quartz" }
                    });
                    break;
                case DatabaseProviders.Sqlite:
                    props.Add(new NameValueCollection{
                        { "quartz.dataSource.default.provider", "SQLite-Microsoft" },
                        { "quartz.jobStore.driverDelegateType", "Quartz.Impl.AdoJobStore.SQLiteDelegate, Quartz" }
                    });
                    break;
                case DatabaseProviders.Oracle:
                    props.Add(new NameValueCollection{
                        { "quartz.dataSource.default.provider", "OracleODPManaged" },
                        { "quartz.jobStore.driverDelegateType", "Quartz.Impl.AdoJobStore.OracleDelegate, Quartz" }
                    });
                    break;
            }

            StdSchedulerFactory factory = new StdSchedulerFactory(props);
            var scheduler = factory.GetScheduler().Result;


            return scheduler;
        }

        public static void Configure(IScheduler scheduler, IServiceProvider provider)
        {
            scheduler.JobFactory = new JobFactory(provider);

            scheduler.ListenerManager.AddJobListener((IJobListener)provider.GetService(typeof(JobListenerBase)));
        }
    }
}
