@inject Binit.Framework.Interfaces.Configuration.IGoogleMapsConfiguration googleMapsConfiguration
@inject Binit.Framework.Interfaces.Configuration.IGoogleAnalyticsConfiguration googleAnalyticsConfiguration
@inject IStringLocalizer<SharedResources> localizer
@using Binit.Framework
@using System.Globalization
@using Microsoft.Extensions.Localization

@{
    string lang = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
    var localizations = localizer.GetAllStrings().Where(ls => ls.Name.StartsWith("WebApp.DevExpress.")).ToDictionary(ls =>
    ls.Name, ls => ls.Value);
}

<!DOCTYPE html>
<html lang="@CultureInfo.CurrentCulture.Name">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <!-- Tell the browser to be responsive to screen width -->
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="Binit Ignite">
    <meta name="author" content="Binit">
    <title>@ViewData["Title"]</title>

    <!-- Favicon icon -->
    <link rel="icon" type="image/png" sizes="16x16"
        href="@Url.Action("Display", "Tenant", new { replace = "isologo-promanager.png" })">

    <!-- Bootstrap Core CSS -->
    <link href="~/lib/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="~/lib/prism/prism.css" rel="stylesheet">

    <!-- Sweet Alert -->
    <link href="~/lib/sweetalert/sweetalert.css" rel="stylesheet">

    <!-- Floating Labels -->
    <link href="~/css/floating-label.css" rel="stylesheet">

    <!-- DevExtreme -->
    <link href="~/css/devextreme/dx.common.css" rel="stylesheet" />
    <link href="~/css/devextreme/dx.light.css" rel="stylesheet" />
    <link href="~/css/devextreme/custom-list.css" rel="stylesheet" />
    <link href="~/css/devextreme/custom/dx.action-buttons.css" rel="stylesheet" />

    <!-- Custom CSS -->
    <link id="custom-css-link" href="~/css/style.css" rel="stylesheet">
    <link href="~/css/notification/custom.css" rel="stylesheet">

    @{
        <script>
            var googleApiKey = @Json.Serialize(googleMapsConfiguration.ApiKey);
        </script>
    }
    <script src="~/js/plugins.js"></script>

    <!-- Add your App's CSS customizations here -->
    <link href="~/css/app.css" rel="stylesheet">

    <!-- You can change the theme colors from here -->
    <link href="~/css/colors/broiler-breeder.css" id="theme" rel="stylesheet">

    <link href="~/css/views/shared/sidebar.css" rel="stylesheet" />

    <link rel="stylesheet" href="~/css/site.css" />
    <link rel="stylesheet" href="~/css/theme.css" />
    <script async src="~/lib/google-tag-manager/gtag.js?id=@googleAnalyticsConfiguration.Id"></script>
    <script>
        var analyiticsId = '@googleAnalyticsConfiguration.Id';
        if (analyiticsId == 'UA-101608081-3' || !analyiticsId) {
            console.error('The AnalyticsID is provided by the template, please change it.')
        }
        else {
            window.dataLayer = window.dataLayer || [];
            function gtag() { dataLayer.push(arguments); }
            gtag('js', new Date());

            gtag('config', analyiticsId);
        }
    </script>
    @Html.Raw(JavaScriptSnippet.FullScript)
    <script src="~/lib/jquery/jquery.min.js"></script>

    <script>
        var devExpressCustomLocalizations = JSON.parse('@Html.Raw(Json.Serialize(localizations))');
    </script>

    <!-- Dev Extreme plugins -->
    <script src="~/js/devextreme/custom/dx.actionButtons.js"></script>
    <script src="~/js/devextreme/custom/dx.cellTemplates.js"></script>
    <script src="~/js/devextreme/custom/dx.dropdownActionButtonCellTemplate.js"></script>
    <script src="~/js/devextreme/custom/dx.dropdownActionButtons.js"></script>
    <script src="~/js/devextreme/custom/dx.toolbar.js"></script>
    <script src="~/js/devextreme/custom/dx.excelExporter.js"></script>
    <script src="~/js/devextreme/custom/dx.initializer.js"></script>
    <script src="~/js/devextreme/jszip.min.js"></script>
    <script src="~/js/devextreme/dx.all.js"></script>
    <script src="~/js/devextreme/dx.aspnet.mvc.js"></script>
    <script src="~/js/devextreme/dx.aspnet.data.js"></script>
    <!-- Dev Extreme localization -->
    <script src="~/js/devextreme/localization/dx.messages.en.js"></script>
    <script src="~/js/devextreme/localization/dx.messages.es.js"></script>
    <script src="~/js/devextreme/localization/dx.messages.pt.js"></script>
    <script>
        DevExpress.localization.locale('@lang');
    </script>

    <!-- Scripts for DataGrid exporting-->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/exceljs/3.3.1/exceljs.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/1.3.8/FileSaver.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/babel-polyfill/7.4.0/polyfill.min.js"></script>

</head>

<body class="fix-header fix-sidebar">

    @{
        await Html.RenderPartialAsync("_Spinner");
    }
    @{
        await Html.RenderPartialAsync("_Navbar");
    }
    @{
        await Html.RenderPartialAsync("_Sidebar");
    }

    <!-- Page wrapper  -->
    <div class="page-wrapper">
        @RenderBody()
    </div>
    <!-- End Page wrapper  -->
    <script src="~/lib/jquery-cookie/jquery.cookie.js"></script>
    <script src="~/lib/popper/popper.min.js"></script>
    <script src="~/lib/bootstrap/js/bootstrap.min.js"></script>
    <script src="~/lib/perfect-scrollbar/js/perfect-scrollbar.jquery.min.js"></script>

    <!-- Sweet Alert -->
    <script src="~/lib/sweetalert/sweetalert.min.js"></script>
    <script src="~/lib/sweetalert/jquery.sweet-alert.custom.js"></script>

    <!-- Lodash -->
    <script src="~/lib/lodash/lodash.min.js"></script>

    <script src="~/js/site.js" asp-append-version="true"></script>

    <!-- Add your App's JS customizations here -->
    <script src="~/js/app.js" asp-append-version="true"></script>
    <script src="~/js/claimSelection.js"></script>
    <script src="~/js/custom.js"></script>
    <script src="~/js/sidebarmenu.js"></script>
    <script src="~/js/waves.js"></script>
    <script src="~/js/entityDraft.js"></script>
    <script src="~/js/entityAlias.js"></script>
    <script src="~/js/entityTag.js"></script>
    <script src="~/js/notification.js"></script>

    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }
    @RenderSection("Scripts", required: false)
</body>

</html>
