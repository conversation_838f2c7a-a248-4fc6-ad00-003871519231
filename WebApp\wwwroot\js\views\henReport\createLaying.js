$(document).ready(function () {
  // initialize report table if there is only one farm
  if ($("#FarmId").get(0).options.length == 2) {
    loadActiveHenBatches();
  }

  // set date to today
  $("#Date").val(
    new Date().toISOString().split("T")[0].split("-").reverse().join("/")
  );
  setTimeout(() => {
    $("#Date").focus();
    setTimeout(() => {
      $("#Date").blur();
    }, 250);
  }, 250);

  $("#FarmId").on("change", async function () {
    loadActiveHenBatches();
  });

  $("#HenBatchId").on("change", async function () {
    const henBatchId = $(this).val();
    const date = $("#Date").val();

    if (henBatchId) {
      await initializeReportTable();

      if (date && currentTable) {
        await loadGad();
      }
    } else {
      clearReportTable();
      $("#report-table").hide();
    }
  });

  $("#Date").on("blur", async function () {
    const date = $(this).val();
    const henBatchId = $("#HenBatchId").val();

    lastReportDateShown = false;

    if (date && henBatchId) {
      await initializeReportTable();

      if (currentTable) {
        await loadGad();
      }
    } else {
      clearReportTable();
      $("#report-table").hide();
    }
  });

  $("#HenBatchId").on("change", function () {
    lastReportDateShown = false;
  });

  $("#createButton").on("click", async function () {
    await createReport();
  });
});

let currentTable = null;
let reportData = [];
const deviationMap = new Map();
let eggTypes = [];
let firstProductionDateFlag = false;
let firstHenReport = false;

// Filters ---------------------------------------------------------------
async function loadActiveHenBatches() {
  clearReportTable();

  const farmId = $("#FarmId").val();
  const activeHenBatches = await getActiveHenBatchesByFarm(farmId);

  const $henBatchSelect = $("#HenBatchId");
  $henBatchSelect.prop("disabled", false);
  $henBatchSelect.empty();

  // Add default option
  $henBatchSelect.append(
    $("<option></option>").val("").text("Selecione um lote")
  );

  // Add active hen batches
  activeHenBatches.forEach((henBatch) => {
    $henBatchSelect.append(
      $("<option></option>").val(henBatch.id).text(henBatch.code)
    );
  });

  // If there's only one hen batch, select it automatically
  if (activeHenBatches.length === 1) {
    $henBatchSelect.val(activeHenBatches[0].id).trigger("change");
    $henBatchSelect.prop("disabled", true);
  }
}

// Report table ------------------------------------------------------------
const columns = [
  { data: "warehouse", type: "text", editor: false, readOnly: true },
  { data: "line", type: "text", editor: false, readOnly: true },
  // { data: "henBatch", type: "text", editor: false, readOnly: true },
  {
    data: "waterConsumption",
    type: "numeric",
    allowInvalid: false,
    validator: (value, callback) => callback(value >= 0),
  },
  {
    data: "waterPh",
    type: "numeric",
    validator: (value, callback) => callback(value >= 0 && value <= 14),
    allowInvalid: false,
  },
  {
    data: "waterChlorineConcentration",
    type: "numeric",
    validator: (value, callback) => callback(value >= 0),
    allowInvalid: false,
  },
  {
    data: "waterPillQuantity",
    type: "numeric",
    validator: (value, callback) => callback(value >= 0 && value <= 100),
    allowInvalid: false,
  },
  {
    data: "minTemp",
    type: "numeric",
    validator: (value, callback) => callback(value >= 0 && value <= 60),
    allowInvalid: false,
  },
  {
    data: "maxTemp",
    type: "numeric",
    validator: (value, callback) => callback(value >= 0 && value <= 60),
    allowInvalid: false,
  },
  {
    data: "humidity",
    type: "numeric",
    validator: (value, callback) => callback(value >= 0 && value <= 100),
    allowInvalid: false,
  },
  // Ovos Incubáveis (Hatchable Eggs)
  {
    data: "cleanNestEggs",
    type: "numeric",
    validator: (value, callback) => callback(value >= 0),
    allowInvalid: false,
  },
  {
    data: "dirtyNestEggs",
    type: "numeric",
    validator: (value, callback) => callback(value >= 0),
    allowInvalid: false,
  },
  {
    data: "bedEggs",
    type: "numeric",
    validator: (value, callback) => callback(value >= 0),
    allowInvalid: false,
  },
  // Ovos Comerciais (Commercial Eggs)
  {
    data: "doubleYolkEggs",
    type: "numeric",
    validator: (value, callback) => callback(value >= 0),
    allowInvalid: false,
  },
  {
    data: "smallEggs",
    type: "numeric",
    validator: (value, callback) => callback(value >= 0),
    allowInvalid: false,
  },
  {
    data: "defectiveEggs",
    type: "numeric",
    validator: (value, callback) => callback(value >= 0),
    allowInvalid: false,
  },
  {
    data: "dirtyRolledEggs",
    type: "numeric",
    validator: (value, callback) => callback(value >= 0),
    allowInvalid: false,
  },
  {
    data: "crackedEggs",
    type: "numeric",
    validator: (value, callback) => callback(value >= 0),
    allowInvalid: false,
  },
  {
    data: "thinShellEggs",
    type: "numeric",
    validator: (value, callback) => callback(value >= 0),
    allowInvalid: false,
  },
  {
    data: "eliminatedBrokenEggs",
    type: "numeric",
    validator: (value, callback) => callback(value >= 0),
    allowInvalid: false,
  },
  {
    data: "feedIntakeFemale",
    type: "numeric",
    validator: (value, callback) => callback(value >= 0),
    allowInvalid: false,
  },
  {
    data: "feedIntakeOriginFemale",
    type: "dropdown",
    source: async function (_, process) {
      const rowData = this.instance.getSourceDataAtRow(this.row);
      process(rowData.feedIntakeOrigins.map((origin) => origin.name));
    },
    allowInvalid: false,
  },
  {
    data: "feedIntakeMale",
    type: "numeric",
    validator: (value, callback) => callback(value >= 0),
    allowInvalid: false,
  },
  {
    data: "feedIntakeOriginMale",
    type: "dropdown",
    source: async function (_, process) {
      const rowData = this.instance.getSourceDataAtRow(this.row);
      process(rowData.feedIntakeOrigins.map((origin) => origin.name));
    },
    allowInvalid: false,
  },
  {
    data: "deadFemale",
    type: "numeric",
    allowInvalid: false,
  },
  {
    data: "deadMale",
    type: "numeric",
    allowInvalid: false,
  },
];

const columnsMap = new Map(
  columns.map((column, index) => [column.data, { ...column, index }])
);

const deadFemaleColumnIndex = columnsMap.get("deadFemale").index;
const deadMaleColumnIndex = columnsMap.get("deadMale").index;
const feedIntakeFemaleColumnIndex = columnsMap.get("feedIntakeFemale").index;
const feedIntakeMaleColumnIndex = columnsMap.get("feedIntakeMale").index;
const feedIntakeOriginFemaleColumnIndex = columnsMap.get(
  "feedIntakeOriginFemale"
).index;
const feedIntakeOriginMaleColumnIndex = columnsMap.get(
  "feedIntakeOriginMale"
).index;

async function initializeReportTable() {
  clearReportTable();

  const henBatchId = $("#HenBatchId").val();
  const date = $("#Date").val();

  if (date) {
    const isDateValid = await validateDate();
    if (!isDateValid) {
      if (!firstProductionDateFlag) {
        return;
      }
    }
  }

  if (eggTypes.length === 0) {
    eggTypes = await getEggTypes();
  }

  const warehouses = await getWarehousesWithLinesByBatch(henBatchId);
  if (warehouses.length === 0) {
    $("#report-table").hide();
    return;
  }

  // Load deviation parameters before showing the table
  await loadDeviationParameters();

  $("#report-table").show();

  reportData = [];

  warehouses.forEach((warehouse) => {
    warehouse.lines.forEach((line) => {
      if (line.henBatches.length == 1) {
        const henBatch = line.henBatches[0];
        const feedIntakeOrigins = henBatch.feedIntakeOrigins;

        reportData.push({
          warehouse: warehouse.code,
          line: line.code,
          henBatch: henBatch.code,
          henBatchId: henBatch.id,
          henAmountFemale: henBatch.henAmountFemale,
          henAmountMale: henBatch.henAmountMale,
          feedIntakeOrigins: feedIntakeOrigins,
          feedIntakeOriginFemale:
            feedIntakeOrigins.length === 1
              ? feedIntakeOrigins[0].name
              : feedIntakeOrigins.find((origin) => origin.code === "F")?.name,
          feedIntakeOriginMale:
            feedIntakeOrigins.length === 1
              ? feedIntakeOrigins[0].name
              : feedIntakeOrigins.find((origin) => origin.code === "M")?.name,
        });
      }
    });
  });

  const tableElement = $("#report-table").get(0);
  currentTable = new Handsontable(tableElement, {
    data: reportData,
    columns: columns,
    rowHeaders: false,
    nestedHeaders: [
      [
        { label: "<strong>DISTRIBUIÇÃO</strong>", colspan: 2 },
        { label: "<strong>CONDIÇÕES AMBIENTAIS</strong>", colspan: 7 },
        { label: "<strong>OVOS INCUBÁVEIS</strong>", colspan: 3 },
        { label: "<strong>OVOS COMERCIAIS</strong>", colspan: 7 },
        { label: "<strong>CONSUMO ALIMENTO</strong>", colspan: 4 },
        { label: "<strong>MORTALIDADE</strong>", colspan: 2 },
      ],
      [
        //DISTRIBUIÇÃO
        "Aviário",
        "Box",
        //CONDIÇÕES AMBIENTAIS
        "Água",
        "ph/PH",
        "PPM <br>Cloro",
        "Rep. <br>Pastilha",
        "Temp. <br>Mínima",
        "Temp. <br>Máxima",
        "Umidade %",
        //OVOS INCUBÁVEIS
        "Ninho Limpo",
        "Ninho Sujo",
        "Cama",
        //OVOS COMERCIAIS
        "2 Gemas",
        "Pequeno Grj",
        "Defeituoso/Deformado",
        "Sujo/Rolados",
        "Trincado Grj",
        "Casca Fina",
        "Eliminado/Quebrado na GM",
        //CONSUMO ALIMENTO
        "Alimento <br>Fêmeas",
        "Origem <br>Fêmeas",
        "Alimento <br>Machos",
        "Origem <br>Machos",
        "Fêmeas <br>Mortas",
        "Machos <br>Mortos",
      ],
    ],
    filters: false,
    fillHandle: false,
    dropdownMenu: false,
    autoColumnSize: true,
    manualColumnResize: true,
    manualRowResize: true,
    formulas: true,
    width: "100%",
    height: "auto",
    stretchH: "all",
    licenseKey: "non-commercial-and-evaluation",
    // make feed intake and dead hens read only if hen amount is 0
    cells: function (row, col) {
      const rowData = reportData[row];
      if (rowData.henAmountFemale === 0) {
        return {
          readOnly:
            col === feedIntakeFemaleColumnIndex ||
            col === feedIntakeOriginFemaleColumnIndex ||
            col === deadFemaleColumnIndex,
        };
      }
      if (rowData.henAmountMale === 0) {
        return {
          readOnly:
            col === feedIntakeMaleColumnIndex ||
            col === feedIntakeOriginMaleColumnIndex ||
            col === deadMaleColumnIndex,
        };
      }
      return {};
    },
    afterChange: (changes, source) => {
      if (source === "loadData") {
        return;
      }

      const propertiesToCopy = [
        "waterPh",
        "waterChlorineConcentration",
        "waterPillQuantity",
        "minTemp",
        "maxTemp",
        "humidity",
      ];

      const propertiesToDistribute = ["waterConsumption"];

      const eggProperties = [
        "cleanNestEggs",
        "dirtyNestEggs",
        "bedEggs",
        "doubleYolkEggs",
        "smallEggs",
        "defectiveEggs",
        "dirtyRolledEggs",
        "crackedEggs",
        "thinShellEggs",
        "eliminatedBrokenEggs",
      ];

      changes?.forEach(([row, prop, _, newValue]) => {
        const rowData = reportData[row];

        // Copy and distribute values
        let valueToApply = null;

        if (propertiesToCopy.includes(prop)) {
          // copy environment conditions to all lines of the warehouse
          valueToApply = newValue;
        } else if (propertiesToDistribute.includes(prop)) {
          // distribute consumption and feed intake to all lines of the warehouse
          const warehouseLinesCount = reportData.filter(
            (item) => item.warehouse === rowData.warehouse
          ).length;
          valueToApply =
            Math.round((newValue * 100) / warehouseLinesCount) / 100; // round to 2 decimal places
        }

        if (valueToApply !== null) {
          reportData.forEach((item) => {
            if (item.warehouse === rowData.warehouse) {
              item[prop] = valueToApply;
            }
          });
        }

        // Validate deviation
        isDeviationValid(rowData.henBatchId, valueToApply ?? newValue, prop);

        // If an egg property changed, recalculate totals
        if (eggProperties.includes(prop)) {
          calculateEggTotals();
        }
      });

      currentTable.loadData(reportData);
    },
  });

  // Check if this is the first hen report for any of the hen batches
  const deviationParameters = await getDeviationParameters(
    reportData.map((item) => item.henBatchId)
  );

  firstHenReport = deviationParameters.every((d) => d.firstHenReport);

  // Calculate initial egg totals
  calculateEggTotals();
}

// Variables to store egg totals
let totalHatchableEggs = 0;
let totalCommercialEggs = 0;
let totalEliminatedBrokenEggs = 0;
let totalEggs = 0;

// Function to calculate egg totals
function calculateEggTotals() {
  if (!reportData || reportData.length === 0) {
    totalHatchableEggs = 0;
    totalCommercialEggs = 0;
    totalEliminatedBrokenEggs = 0;
    totalEggs = 0;
    return;
  }

  // Calculate total hatchable eggs (Ovos Incubáveis)
  totalHatchableEggs = reportData.reduce((sum, item) => {
    return (
      sum +
      (parseInt(item.cleanNestEggs || 0) || 0) +
      (parseInt(item.dirtyNestEggs || 0) || 0) +
      (parseInt(item.bedEggs || 0) || 0)
    );
  }, 0);

  // Calculate total commercial eggs (Ovos Comerciais)
  totalCommercialEggs = reportData.reduce((sum, item) => {
    return (
      sum +
      (parseInt(item.doubleYolkEggs || 0) || 0) +
      (parseInt(item.smallEggs || 0) || 0) +
      (parseInt(item.defectiveEggs || 0) || 0) +
      (parseInt(item.dirtyRolledEggs || 0) || 0) +
      (parseInt(item.crackedEggs || 0) || 0) +
      (parseInt(item.thinShellEggs || 0) || 0)
    );
  }, 0);

  // Calculate total eliminated/broken eggs (Eliminado/Quebrado na GM)
  totalEliminatedBrokenEggs = reportData.reduce((sum, item) => {
    return sum + (parseInt(item.eliminatedBrokenEggs || 0) || 0);
  }, 0);

  // Calculate total eggs
  totalEggs =
    totalHatchableEggs + totalCommercialEggs + totalEliminatedBrokenEggs;

  // Log the totals for debugging
  console.log("Total Eggs:", totalEggs);
  console.log("Total Hatchable Eggs:", totalHatchableEggs);
  console.log("Total Commercial Eggs:", totalCommercialEggs);
  console.log("Total Eliminated/Broken Eggs:", totalEliminatedBrokenEggs);
}

// Function to check for inconsistent egg data entry patterns across warehouses
function validateConsistentEggData() {
  if (!reportData || reportData.length <= 1) {
    // No inconsistency possible with 0 or 1 warehouse
    return { isValid: true };
  }

  // Calculate egg totals for each warehouse
  const warehouseEggTotals = reportData.map((item) => {
    const warehouseEggs =
      (parseInt(item.cleanNestEggs || 0) || 0) +
      (parseInt(item.dirtyNestEggs || 0) || 0) +
      (parseInt(item.bedEggs || 0) || 0) +
      (parseInt(item.doubleYolkEggs || 0) || 0) +
      (parseInt(item.smallEggs || 0) || 0) +
      (parseInt(item.defectiveEggs || 0) || 0) +
      (parseInt(item.dirtyRolledEggs || 0) || 0) +
      (parseInt(item.crackedEggs || 0) || 0) +
      (parseInt(item.thinShellEggs || 0) || 0) +
      (parseInt(item.eliminatedBrokenEggs || 0) || 0);

    return {
      warehouse: item.warehouse,
      line: item.line,
      totalEggs: warehouseEggs,
    };
  });

  // Check if some warehouses have eggs while others don't
  const warehousesWithEggs = warehouseEggTotals.filter((w) => w.totalEggs > 0);
  const warehousesWithoutEggs = warehouseEggTotals.filter(
    (w) => w.totalEggs === 0
  );

  if (warehousesWithEggs.length > 0 && warehousesWithoutEggs.length > 0) {
    // Inconsistency detected - some warehouses have eggs while others don't
    return {
      isValid: false,
      warehousesWithEggs: warehousesWithEggs,
      warehousesWithoutEggs: warehousesWithoutEggs,
    };
  }

  return { isValid: true };
}

function clearReportTable() {
  if (currentTable && !currentTable.isDestroyed) {
    currentTable.destroy();
    currentTable = null;
  }

  reportData = [];

  totalHatchableEggs = 0;
  totalCommercialEggs = 0;
  totalEliminatedBrokenEggs = 0;
  totalEggs = 0;
}

// GAD -----------------------------------------------------------------
let currentBatchId = null;
let currentDate = null;

const loadGad = async () => {
  const henBatchId = $("#HenBatchId").val();
  const date = $("#Date").val();

  if (!henBatchId || !date) {
    return;
  }

  // prevent reloading if henBatchId and date have not changed
  if (henBatchId === currentBatchId && date === currentDate) {
    return;
  }
  currentBatchId = henBatchId;
  currentDate = date;

  const gad = await getPlannedGADByBatch(henBatchId, date);
  const gadMap = new Map();
  gad.forEach((item) => {
    gadMap.set(item.henbatchId, item);
  });

  reportData.forEach((item) => {
    const gadItem = gadMap.get(item.henBatchId);
    if (gadItem) {
      item.feedIntakeFemale = gadItem.programFemale;
      item.feedIntakeMale = gadItem.programMale;
    }
  });

  if (currentTable) {
    currentTable.loadData(reportData);
  }
};

// Deviation parameters -------------------------------------------------
const loadDeviationParameters = async () => {
  const deviationParameters = await getDeviationParameters(
    reportData.map((item) => item.henBatchId)
  );

  deviationMap.clear();
  deviationParameters.forEach((item) => {
    deviationMap.set(item.henBatchId, item);
  });
};

const isDeviationValid = (
  henBatchId,
  value,
  property,
  displayMessage = true
) => {
  // Skip validation if this is the first hen report
  if (firstHenReport) {
    return true;
  }

  const deviationParameters = deviationMap.get(henBatchId);

  if (deviationParameters) {
    const maxValue = deviationParameters[`${property}Max`] ?? 0;
    const minValue = deviationParameters[`${property}Min`] ?? 0;
    const valueToCompare = value ?? 0;

    if (valueToCompare > maxValue) {
      if (displayMessage) {
        swal.fire({
          title: "Atenção",
          text: `Valor está acima dos carregados nos relatórios anteriores (${maxValue})`,
          type: "warning",
          toast: true,
          position: "bottom-end",
          showConfirmButton: false,
          timer: 3000,
        });
      }
      return false;
    } else if (valueToCompare < minValue) {
      if (displayMessage) {
        swal.fire({
          title: "Atenção",
          text: `Valor está abaixo dos carregados nos relatórios anteriores (${minValue})`,
          type: "warning",
          toast: true,
          position: "bottom-end",
          showConfirmButton: false,
          timer: 3000,
        });
      }
      return false;
    }
  }
  return true;
};

const validateAllDeviations = () => {
  let isAllValid = true;
  reportData.forEach((item) => {
    Object.keys(item).forEach((property) => {
      if (!isDeviationValid(item.henBatchId, item[property], property, false)) {
        isAllValid = false;
      }
    });
  });
  return isAllValid;
};

// Validation functions -------------------------------------------------
async function checkBatchCapitalizationAlert(henBatchId) {
  if (!henBatchId) return;

  // Check if there's any egg data being registered
  const hasEggData = reportData.some(
    (item) =>
      (item.cleanNestEggs && item.cleanNestEggs > 0) ||
      (item.dirtyNestEggs && item.dirtyNestEggs > 0) ||
      (item.bedEggs && item.bedEggs > 0) ||
      (item.doubleYolkEggs && item.doubleYolkEggs > 0) ||
      (item.smallEggs && item.smallEggs > 0) ||
      (item.defectiveEggs && item.defectiveEggs > 0) ||
      (item.dirtyRolledEggs && item.dirtyRolledEggs > 0) ||
      (item.crackedEggs && item.crackedEggs > 0) ||
      (item.thinShellEggs && item.thinShellEggs > 0) ||
      (item.eliminatedBrokenEggs && item.eliminatedBrokenEggs > 0)
  );

  try {
    const response = await fetch(
      `${location.origin}/HenReport/CheckBatchCapitalizationAlert?henBatchId=${henBatchId}&hasEggData=${hasEggData}`
    );

    if (!response.ok) {
      return;
    }

    const alertMessage = await response.text();

    if (alertMessage && alertMessage.trim() !== "") {
      await Swal.fire({
        title: "Atenção",
        html: alertMessage,
        type: "warning",
        confirmButtonColor: "green",
        confirmButtonText: "OK",
      });
    }
  } catch (error) {}
}

async function validateFirstProductionDate() {
  const date = $("#Date").val();
  const henBatchId = $("#HenBatchId").val();

  // Recalculate egg totals to ensure we have the latest values
  calculateEggTotals();

  // Check if any warehouse has eggs
  const anyWarehouseHasEggs = reportData.some((item) => {
    // Calculate total eggs for this warehouse
    const warehouseEggs =
      parseInt(item.cleanNestEggs || 0) +
      parseInt(item.dirtyNestEggs || 0) +
      parseInt(item.bedEggs || 0) +
      parseInt(item.doubleYolkEggs || 0) +
      parseInt(item.smallEggs || 0) +
      parseInt(item.defectiveEggs || 0) +
      parseInt(item.dirtyRolledEggs || 0) +
      parseInt(item.crackedEggs || 0) +
      parseInt(item.thinShellEggs || 0) +
      parseInt(item.eliminatedBrokenEggs || 0);

    return warehouseEggs > 0;
  });

  // Check for inconsistent egg data entry patterns
  const eggDataValidation = validateConsistentEggData();

  // If total eggs across all warehouses is 0 and no individual warehouse has eggs, validate first production date
  if (totalEggs <= 0 && !anyWarehouseHasEggs && henBatchId) {
    try {
      return new Promise((resolve) => {
        $.ajax({
          type: "GET",
          url: `/HenReport/FirstProductionDateValidation?date=${date}&henBatchesIds=${henBatchId}`,
          success: function () {
            firstProductionDateFlag = true;
            resolve(true);
          },
          error: function (response) {
            firstProductionDateFlag = false;
            Swal.fire({
              html: response.responseText,
              type: "warning",
              allowOutsideClick: false,
              allowEscapeKey: false,
            }).then(() => {
              resolve(false);
            });
          },
        });
      });
    } catch (error) {
      firstProductionDateFlag = false;
      Swal.fire({
        html: "Erro na validação da data de primeira produção",
        type: "warning",
        allowOutsideClick: false,
        allowEscapeKey: false,
      });
      return false;
    }
  } else if (!eggDataValidation.isValid) {
    firstProductionDateFlag = true;
    return true;
  } else {
    firstProductionDateFlag = true;
    return true;
  }
}

async function validateDate() {
  const henBatchId = $("#HenBatchId").val();
  const date = $("#Date").val();

  if (!henBatchId || !date) {
    clearReportTable();
    $("#report-table").hide();
    return false;
  }

  try {
    const result = await validateDateSkipHenAmount(henBatchId, date);
    return result;
  } catch (error) {
    if (error.message && error.message.includes("produção de ovos")) {
      firstProductionDateFlag = true;
    }

    return false;
  }
}

let lastReportDateShown = false;

async function validateDateSkipHenAmount(henBatchId, date) {
  return new Promise(async (resolve, reject) => {
    try {
      const realLastReportDateResponse = await fetch(
        `${location.origin}/HenReport/GetRealLastReportDate?henBatchId=${henBatchId}`
      );

      if (!realLastReportDateResponse.ok) {
        throw new Error("Falha em buscar a data do último relatório");
      }

      const realLastReportDate = await realLastReportDateResponse.text();

      if (!lastReportDateShown) {
        await Swal.fire({
          title: "Informação",
          html: `Data do último relatório: <strong>${realLastReportDate}</strong>`,
          type: "info",
          timer: 5000,
          showConfirmButton: true,
        });
        lastReportDateShown = true;
      }

      const response = await fetch(
        `${
          location.origin
        }/HenReport/ValidateDateSkipHenAmount?date=${encodeURIComponent(
          date
        )}&henBatchId=${henBatchId}`
      );

      if (!response.ok) {
        const errorMessage = await response.text();

        await Swal.fire({
          title: "Erro",
          html: errorMessage,
          type: "error",
          allowOutsideClick: false,
          allowEscapeKey: false,
        });

        clearReportTable();
        $("#report-table").hide();

        reject(new Error(errorMessage));
        return;
      }

      resolve(true);
    } catch (error) {
      await Swal.fire({
        title: "Erro",
        html: "Erro ao validar a data do relatório: " + error.message,
        type: "error",
        allowOutsideClick: false,
        allowEscapeKey: false,
      });

      // Clear the report table and hide it
      clearReportTable();
      $("#report-table").hide();

      // Reject the promise with the error
      reject(error);
    }
  });
}

async function validateBirdMovements() {
  const date = $("#Date").val();
  const henBatchId = $("#HenBatchId").val();

  try {
    return new Promise((resolve) => {
      $.ajax({
        dataType: "json",
        type: "GET",
        url: `/HenReport/BirdMovementValidationFromHenBatch?henBatchId=${henBatchId}&dateHR=${date}`,
        success: function (data) {
          if (data && data !== "") {
            Swal.fire({
              html: `Existem movimentações de aves posteriores à data do relatório para os seguintes boxes: <ul><li>${data}</li></ul>`,
              type: "warning",
              showCancelButton: true,
              confirmButtonColor: "#DD6B55",
              confirmButtonText: "Continuar mesmo assim",
              cancelButtonText: "Cancelar",
              closeOnCancel: true,
            }).then(function (selection) {
              resolve(selection.value);
            });
          } else {
            resolve(true);
          }
        },
        error: function () {
          Swal.fire({
            title: "Erro",
            html: "Erro ao validar movimentações de aves",
            type: "error",
            allowOutsideClick: false,
            allowEscapeKey: false,
          });
          resolve(false);
        },
      });
    });
  } catch (error) {
    Swal.fire({
      title: "Erro",
      html: "Erro ao validar movimentações de aves",
      type: "error",
      allowOutsideClick: false,
      allowEscapeKey: false,
    });
    return false;
  }
}

// Function to validate warehouses and filter valid ones
async function validateWarehouses() {
  const date = $("#Date").val();
  const henBatchId = $("#HenBatchId").val();

  if (!date || !henBatchId || reportData.length === 0) {
    return false;
  }

  const result = await validateAndFilterWarehouses(true);
  return result;
}

async function validateAndFilterWarehouses(showWarnings = true) {
  const date = $("#Date").val();
  const henBatchId = $("#HenBatchId").val();

  if (!date || !henBatchId || reportData.length === 0) {
    return false;
  }

  try {
    const response = await fetch(
      `${location.origin}/HenReport/BirdMovementValidationFromHenBatch?henBatchId=${henBatchId}&dateHR=${date}`
    );
    const data = await response.json();

    if (!data || data === "") {
      return true;
    }

    if (showWarnings) {
      const isConfirmed = await Swal({
        html: `Existem movimentações de aves posteriores à data do relatório para os seguintes boxes: <ul><li>${data}</li></ul>`,
        type: "warning",
        showCancelButton: true,
        confirmButtonColor: "#DD6B55",
        confirmButtonText: "Continuar mesmo assim",
        cancelButtonText: "Cancelar",
        closeOnCancel: true,
      });

      return isConfirmed.value;
    }

    return false;
  } catch (error) {
    if (showWarnings) {
      await Swal({
        html: "Erro ao validar movimentações de aves",
        type: "error",
      });
    }

    return false;
  }
}

function validateFeedIntakeOrigins() {
  let isValid = true;
  const invalidItems = [];

  reportData.forEach((item) => {
    if (
      item.henAmountFemale > 0 &&
      (!item.feedIntakeOriginFemale || item.feedIntakeOriginFemale === "")
    ) {
      isValid = false;
      invalidItems.push(
        `${item.warehouse} - ${item.line}: Origem do consumo de alimento para fêmeas não selecionada`
      );
    }

    if (
      item.henAmountMale > 0 &&
      (!item.feedIntakeOriginMale || item.feedIntakeOriginMale === "")
    ) {
      isValid = false;
      invalidItems.push(
        `${item.warehouse} - ${item.line}: Origem do consumo de alimento para machos não selecionada`
      );
    }
  });

  if (!isValid) {
    Swal({
      html: `As seguintes origens de consumo de alimento não foram selecionadas:<ul>${invalidItems
        .map((item) => `<li>${item}</li>`)
        .join("")}</ul>`,
      type: "warning",
    });
  }

  return isValid;
}

// Create report ---------------------------------------------------------
const createReport = async () => {
  const createButton = $("#createButton");
  const originalText = createButton.text();
  createButton.prop("disabled", true);

  try {
    const isDateValid = await validateDate();
    if (!isDateValid) {
      if (firstProductionDateFlag) {
        $("#report-table").show();
      }

      createButton.prop("disabled", false).text(originalText);
      return;
    }

    const henBatchId = $("#HenBatchId").val();
    if (henBatchId) {
      await checkBatchCapitalizationAlert(henBatchId);
    }

    $("#report-table").show();

    const isFirstProductionDateValid = await validateFirstProductionDate();
    if (!isFirstProductionDateValid) {
      // Ensure the table remains visible
      $("#report-table").show();
      createButton.prop("disabled", false).text(originalText);
      return;
    }

    // Properly await the result of validateBirdMovements
    const isBirdMovementsValid = await validateBirdMovements();
    if (!isBirdMovementsValid) {
      createButton.prop("disabled", false).text(originalText);
      return;
    }

    if (!validateFeedIntakeOrigins()) {
      const isConfirmed = await Swal({
        html: "Algumas origens de consumo de alimento não foram selecionadas. Deseja continuar mesmo assim?",
        type: "warning",
        showCancelButton: true,
        confirmButtonColor: "green",
        confirmButtonText: "Continuar",
        cancelButtonText: "Cancelar",
      });

      if (!isConfirmed.value) {
        createButton.prop("disabled", false).text(originalText);
        return;
      }
    }

    // Check for deviations
    if (!validateAllDeviations()) {
      const isConfirmed = await Swal({
        html: "Alguns dos valores digitados têm um desvio maior que 20% em relação aos valores digitados nos relatórios anteriores; recomenda-se revisar os dados.",
        type: "warning",
        showCancelButton: true,
        confirmButtonColor: "green",
        confirmButtonText: "Criar",
        cancelButtonText: "Revisar",
      });

      if (!isConfirmed.value) {
        createButton.prop("disabled", false).text(originalText);
        return;
      }
    }

    createButton.html('<i class="fa fa-spinner fa-spin"></i> Enviando...');

    const reports = reportData.map((item) => {
      const layingReport = {
        ...item,
        FeedIntakeFemaleOriginId: item.feedIntakeOrigins.find(
          (origin) => origin.name === item.feedIntakeOriginFemale
        )?.id,
        FeedIntakeMaleOriginId: item.feedIntakeOrigins.find(
          (origin) => origin.name === item.feedIntakeOriginMale
        )?.id,
      };

      return layingReport;
    });

    reports.forEach((item) => {
      Object.keys(item).forEach((key) => {
        if (columnsMap.get(key)?.type === "numeric" && !item[key]) {
          item[key] = 0;
        }
      });

      item.ClassifiedEggs = [];

      const addEggType = (materialId, quantity) => {
        if (!quantity) return;
        item.ClassifiedEggs.push({
          MaterialId: materialId,
          Quantity: quantity,
        });
      };

      // Map each egg type to the corresponding field using the exact MaterialIds
      if (item.cleanNestEggs)
        addEggType("C355B802-FBB0-4969-45CC-08D8FABC0A06", item.cleanNestEggs);
      if (item.dirtyNestEggs)
        addEggType("D2006215-43F0-40B3-45CD-08D8FABC0A06", item.dirtyNestEggs);
      if (item.bedEggs)
        addEggType("90D811EE-4FC1-435E-45CE-08D8FABC0A06", item.bedEggs);
      if (item.doubleYolkEggs)
        addEggType("D6545A08-82B7-4D7C-45CF-08D8FABC0A06", item.doubleYolkEggs);
      if (item.smallEggs)
        addEggType("FDF5D866-22F0-4F26-45D0-08D8FABC0A06", item.smallEggs);
      if (item.defectiveEggs)
        addEggType("CEA6A1CC-65F4-45ED-45D1-08D8FABC0A06", item.defectiveEggs);
      if (item.dirtyRolledEggs)
        addEggType(
          "99B49505-9740-46E0-45D2-08D8FABC0A06",
          item.dirtyRolledEggs
        );
      if (item.crackedEggs)
        addEggType("8B541005-28E6-44CC-45D3-08D8FABC0A06", item.crackedEggs);
      if (item.thinShellEggs)
        addEggType("60DB4E62-4F18-41ED-45D6-08D8FABC0A06", item.thinShellEggs);
      if (item.eliminatedBrokenEggs)
        addEggType(
          "7B1BC3D9-B32B-4A42-45D4-08D8FABC0A06",
          item.eliminatedBrokenEggs
        );
    });

    // Calculate egg totals before sending
    calculateEggTotals();

    // Check if any warehouse has eggs
    const anyWarehouseHasEggs = reportData.some((item) => {
      // Calculate total eggs for this warehouse
      const warehouseEggs =
        parseInt(item.cleanNestEggs || 0) +
        parseInt(item.dirtyNestEggs || 0) +
        parseInt(item.bedEggs || 0) +
        parseInt(item.doubleYolkEggs || 0) +
        parseInt(item.smallEggs || 0) +
        parseInt(item.defectiveEggs || 0) +
        parseInt(item.dirtyRolledEggs || 0) +
        parseInt(item.crackedEggs || 0) +
        parseInt(item.thinShellEggs || 0) +
        parseInt(item.eliminatedBrokenEggs || 0);

      return warehouseEggs > 0;
    });

    // If total eggs is 0 but some individual warehouse has eggs, this is inconsistent
    if (totalEggs <= 0 && anyWarehouseHasEggs) {
      await Swal.fire({
        title: "Erro",
        html: "Inconsistência nos dados de ovos. Por favor, verifique os valores inseridos.",
        type: "error",
        allowOutsideClick: false,
        allowEscapeKey: false,
      });
      createButton.prop("disabled", false).text(originalText);
      return;
    }

    // Check for inconsistent egg data entry patterns across warehouses
    const eggDataValidation = validateConsistentEggData();
    if (!eggDataValidation.isValid) {
      // Check if this batch has already had its first production date
      const henBatchId = $("#HenBatchId").val();
      let hasFirstProductionDate = false;

      try {
        const response = await fetch(
          `${location.origin}/HenBatch/HasFirstProductionDate?henBatchId=${henBatchId}`
        );
        if (response.ok) {
          hasFirstProductionDate = await response.json();
        }
      } catch (error) {
        console.error("Error checking first production date:", error);
      }

      // Format the warehouse lists for display
      const warehousesWithEggsList = eggDataValidation.warehousesWithEggs
        .map((w) => `<li>${w.warehouse} - ${w.line}</li>`)
        .join("");

      const warehousesWithoutEggsList = eggDataValidation.warehousesWithoutEggs
        .map((w) => `<li>${w.warehouse} - ${w.line}</li>`)
        .join("");

      let errorTitle = "Erro";
      let errorMessage = `
        <p>Por favor, insira os dados para criar o relatório.</p>
        <p>Todos os aviários devem ter valores consistentes de produção de ovos.</p>
      `;
      if (hasFirstProductionDate) {
        errorTitle = "Erro";
        errorMessage = `
          <p><strong>Este lote já registrou sua primeira produção de ovos.</strong></p>
          <p>Após a primeira produção de ovos, todos os aviários devem ter dados de produção de ovos.</p>
          <p>Por favor, insira os dados para criar o relatório.</p>
        `;
      }

      await Swal.fire({
        title: errorTitle,
        html: errorMessage,
        type: "error",
        confirmButtonText: "Revisar",
        allowOutsideClick: false,
        allowEscapeKey: false,
      });

      createButton.prop("disabled", false).text(originalText);
      return;
    }

    // Add egg totals to the request
    const requestData = {
      reportDate: $("#Date").val(),
      reports: reports,
      totalHatchableEggs: totalHatchableEggs,
      totalCommercialEggs: totalCommercialEggs,
      totalEliminatedBrokenEggs: totalEliminatedBrokenEggs,
      totalEggs: totalEggs,
    };

    await createHenReportsFromTable(requestData);
  } catch (error) {
    await Swal.fire({
      title: "Erro",
      html: error.message || "Ocorreu um erro ao criar o relatório",
      type: "error",
    });
  } finally {
    // Restore button state
    createButton.prop("disabled", false).text(originalText);
  }
};

// API calls ------------------------------------------------------------
const getActiveHenBatchesByFarm = async (farmId) => {
  const response = await fetch(
    `${location.origin}/HenReport/GetActiveHenBatchesByFarm?farmId=${farmId}&henStage=3`
  );

  return response.json();
};

const getEggTypes = async () => {
  const response = await fetch(`${location.origin}/api/Material/egg-types`);
  return response.json();
};

const getWarehousesWithLinesByBatch = async (henBatchId) => {
  if (!henBatchId) {
    return [];
  }

  const response = await fetch(
    `${location.origin}/HenReport/GetWarehousesWithLinesByBatch?henBatchId=${henBatchId}`
  );

  return response.json();
};

const getPlannedGADByBatch = async (henBatchId, date) => {
  const response = await fetch(
    `${location.origin}/HenReport/GetPlannedGADByBatch?henBatchId=${henBatchId}&date=${date}`
  );

  return response.json();
};

const getDeviationParameters = async (henBatchIds) => {
  const response = await fetch(
    `${location.origin}/HenReport/CreateDeviationDTOs?ids=${henBatchIds.join(
      ","
    )}`
  );

  return response.json();
};

const createHenReportsFromTable = async (requestData) => {
  try {
    const response = await fetch(
      `${location.origin}/HenReport/CreateLayingReportsFromTable`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestData),
      }
    );

    const result = await response.json();

    if (!response.ok) {
      let errorMessage = result.message || "Erro ao criar lançamentos";

      if (result.error) {
        errorMessage += `: ${result.error}`;
      }

      throw new Error(errorMessage);
    }

    await Swal({
      title: "Sucesso",
      text: "Lançamentos criados com sucesso",
      type: "success",
      confirmButtonColor: "green",
      confirmButtonText: "OK",
    });

    redirectAfterCreation(result.henReports);
  } catch (error) {
    throw error;
  }
};

const redirectAfterCreation = (reports) => {
  const reportsWithDeadHen = reports
    .filter((report) => report.totalDeaths > 0 || report.totalDepopulations > 0)
    .map((report) => report.id);

  if (reportsWithDeadHen.length > 0) {
    const henReportsId = reportsWithDeadHen.join(",");
    window.location.href = `${location.origin}/HenReport/EstablishDeathAndDepopulationQuantitiesReasons?henStage=Laying&henReportsId=${henReportsId}`;
  } else {
    window.location.href = `${location.origin}/HenReport?henStage=Laying`;
  }
};
