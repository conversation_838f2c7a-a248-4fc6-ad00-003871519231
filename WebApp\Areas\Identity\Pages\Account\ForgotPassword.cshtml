@page
@using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Areas.Identity.Pages.Account.ForgotPassword
@inject Microsoft.Extensions.Localization.IStringLocalizer<Binit.Framework.SharedResources> localizer
@model ForgotPasswordModel
@{
    ViewData["Title"] = localizer[Lang.Title];
    Layout = "/Views/Shared/_ExternalLayout.cshtml";
}

<div id="login" class="d-flex align-items-center justify-content-center vh-100">
    <div class="card-body bg-white h-100" style="max-width: 55rem;">
        <form id="account" method="post"
            class="floating-labels d-flex flex-column align-items-center justify-content-center h-100">
            <a href="javascript:void(0)" class="text-center db">
                <img src="@Url.Action("Display", "Tenant", new { replace = "logo-promanager.png" })"
                    style="margin-right: 1.5rem;" width="150" alt="Home" />
            </a>

            <!-- Inputs container -->
            <div class="login-container p-5 px-md-5 rounded" style="max-width: 500px; width: 100%;">
                <h3 class="box-title mb-4">@ViewData["Title"]</h3>
                <small class="mb-4 d-block">@localizer[Lang.Subtitle]</small>

                <!-- Email -->
                <div class="form-group mb-5 px-3">
                    <div class="col-xs-12">
                        <label asp-for="Input.Email"
                            style="position: static; display: block; margin-bottom: 5px;"></label>
                        <input asp-for="Input.Email" class="form-control" type="text" required
                            style="border: 1px solid #ccc; border-radius: 4px; padding: 8px;">
                        <span class="bar"></span>
                        <span asp-validation-for="Input.Email" class="text-danger"></span>
                    </div>
                </div>

                <div class="form-group text-center mt-5 px-3">
                    <div class="col-xs-12">
                        <button class="btn btn-themecolor btn-block text-uppercase btn-rounded py-2" type="submit"
                            style="font-size: 1rem;">@localizer[Lang.SubmitButton]</button>
                    </div>
                </div>
                <div class="form-group mb-0 text-center">
                    <a asp-page="./Login" class="text-dark"><strong>@localizer[Lang.BackToLogin]</strong></a>
                </div>
            </div>
        </form>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
