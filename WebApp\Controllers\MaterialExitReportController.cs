using Binit.Framework;
using Binit.Framework.Constants.Authentication;
using Binit.Framework.Constants.SeedEntities;
using Binit.Framework.ExceptionHandling.Types;
using Binit.Framework.Helpers;
using Binit.Framework.Interfaces.DAL;
using Binit.Framework.Interfaces.ExceptionHandling;
using DevExtreme.AspNet.Data;
using DevExtreme.AspNet.Data.ResponseModel;
using DevExtreme.AspNet.Mvc;
using Domain.Entities.Model;
using Domain.Entities.Model.Enum;
using Domain.Logic.BusinessLogic;
using Domain.Logic.BusinessLogic.DTOs;
using Domain.Logic.DTOs.Export;
using Domain.Logic.Interfaces;
using Domain.Logic.Reporting.SendEggs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Newtonsoft.Json;
using Oracle.EntityFrameworkCore.Query.Internal;
using Quartz;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using WebApp.Models;
using WebApp.WebTools.DataGrid;
using JsLang = Binit.Framework.Localization.LocalizationConstants.WebApp.Js;
using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Controllers.MaterialExitReportController;

namespace WebApp.Controllers
{
    [Authorize]
    public class MaterialExitReportController : Controller
    {
        private readonly IStringLocalizer<SharedResources> localizer;
        private readonly IOperationContext operationContext;
        private readonly IPersonService personService;
        private readonly IMaterialExitReportService materialExitReportService;
        private readonly IMaterialService materialService;
        private readonly IMaterialExitReportBusinessLogic materialExitReportBusinessLogic;
        private readonly ICapacityUnitService capacityUnitService;
        private readonly IContainerService<Container> containerService;
        private readonly ICapacityUnitBusinessLogic capacityUnitBusinessLogic;
        private readonly IExceptionManager exceptionManager;
        private readonly ISlaughterhouseService slaughterhouseService;
        private readonly IHenBatchService henBatchService;

        public MaterialExitReportController(
            IStringLocalizer<SharedResources> localizer,
            IPersonService personService,
            IOperationContext operationContext,
            IMaterialExitReportService materialExitReportService,
            IMaterialExitReportBusinessLogic materialExitReportBusinessLogic,
            ICapacityUnitService capacityUnitService,
            ICapacityUnitBusinessLogic capacityUnitBusinessLogic,
            IContainerService<Container> containerService,
            IMaterialService materialService,
            IExceptionManager exceptionManager,
            ISlaughterhouseService slaughterhouseService,
            IHenBatchService henBatchService)
        {
            this.localizer = localizer;
            this.operationContext = operationContext;
            this.personService = personService;
            this.materialService = materialService;
            this.materialExitReportService = materialExitReportService;
            this.materialExitReportBusinessLogic = materialExitReportBusinessLogic;
            this.capacityUnitService = capacityUnitService;
            this.capacityUnitBusinessLogic = capacityUnitBusinessLogic;
            this.containerService = containerService;
            this.exceptionManager = exceptionManager;
            this.slaughterhouseService = slaughterhouseService;
            this.henBatchService = henBatchService;
        }

        #region Index 
        public async Task<IActionResult> Index(AreaEnum? area = null, string validationError = "", AreaEnum? destinationArea = null)
        {
            if (!CheckAuthorizationForUsers(area))
                return Forbid();

            ViewData["Title"] = area == AreaEnum.Laying && destinationArea == AreaEnum.Classification
                ? localizer[Lang.IndexEggsTitle]
                : localizer[Lang.IndexTitle];
            ViewData["Area"] = area.HasValue ? area.Value.ToString() : "";
            ViewData["DestinationArea"] = destinationArea.HasValue ? destinationArea.Value.ToString() : "";
            ViewData["UserIsAdmin"] = CheckAuthorizationForAdministrator(area).ToString();
            ViewData["URL"] = $"/MaterialExitReport?area={area}&destinationArea={destinationArea}";
            ViewData["TableId"] = $"materialExit-report-table-{area}";

            await SetFilters(area);

            if (!string.IsNullOrEmpty(validationError))
                ViewData["ValidationError"] = validationError;

            return View();
        }

        private async Task SetFilters(AreaEnum? area)
        {
            ViewData["Origins"] = materialExitReportBusinessLogic.GetOriginsByArea(area);
            ViewData["Destinations"] = materialExitReportBusinessLogic.GetDestinationByArea(area);
            ViewData["Identifiers"] = await GetIdentifiers();
        }
        #endregion

        #region Get
        [HttpGet]
        public async Task<JsonResult> GetAll(DataSourceLoadOptions loadOptions, Dictionary<string, string> filters)
        {
            FilterMaterialExitReportDTO filter = new FilterMaterialExitReportDTO(filters);

            IQueryable<MaterialExitReportDTO> query = materialExitReportBusinessLogic.GetAllMaterialExitReports(filter);

            LoadResult response = DataSourceLoader.Load(await query.ToListAsync(), loadOptions);

            return new JsonResult(response);
        }

        #endregion

        public async Task<IActionResult> Export(DataSourceLoadOptions loadOptions,
            Dictionary<string, string> filters,
            [FromServices] IScheduler scheduler,
            [FromServices] ISpreadSheetExportService spreadSheetExportService)
        {
            filters.Add("entity", nameof(MaterialExitReportExport));
            filters.Add("host", Request.Host.Value);
            filters.Add("scheme", Request.Scheme);

            int totalCount = int.Parse(filters["totalCount"]);

            if (totalCount < 2000)
            {
                var (uri, file) = await spreadSheetExportService.Export(loadOptions, filters);
                return Created(uri, new { file });
            }
            else
            {
                string loadOptionsJson = JsonConvert.SerializeObject(loadOptions);
                string filtersJson = JsonConvert.SerializeObject(filters);
                string email = operationContext.GetUsername();

                JobDataMap data = new JobDataMap
                {
                    ["loadOptions"] = loadOptionsJson,
                    ["filters"] = filtersJson,
                    ["email"] = email,
                    ["culture"] = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName

                };
                await scheduler.TriggerJob(new JobKey("ExportJob", "Startup"), data);

                return Accepted(new
                {
                    message = localizer.GetString(
                    Binit.Framework.Localization.LocalizationConstants.DomainLogic.JobScheduler.Jobs.ExportJob.ExportResultMessage,
                    email).Value
                });
            }
        }

        #region Create 
        public IActionResult Create(AreaEnum? area, string originId = null, bool onlyEggs = false, string referURL = "")
        {
            if (!CheckAuthorizationForAdministrator(area))
                return Forbid();

            // Setup View Title and Mode
            ViewData["Title"] = localizer[Lang.CreateTitle];
            ViewData["Action"] = "Create";
            ViewData["MaterialExitReportResources"] = JsLocalizer.GetLocalizedResources(JsLang.MaterialExitReportCreate, this.localizer);

            if (referURL.Contains("."))
                referURL = referURL.Replace(".", "&");

            MaterialExitReportViewModel model = new MaterialExitReportViewModel()
            {
                Area = area.ToString(),
                OriginId = originId,
                UrlDestination = originId,
                OnlyEggs = onlyEggs,
                ReferURL = referURL
            };

            if (!string.IsNullOrEmpty(originId))
            {
                Container originContainer = containerService.Get(new Guid(originId));
                if (originContainer is HenBatch origin && (origin.HenAmountFemale + origin.HenAmountMale == 0))
                    return RedirectToAction("Index", "HenBatch", new { henStage = area, validationError = "4" });
                if (originContainer.ContainerType == ContainerTypes.HenBatch)
                    model.OriginIsHenBatch = true;
            }

            string error = InitLists(model);
            if (!string.IsNullOrEmpty(error))
            {
                if (error == "1")
                    return RedirectToAction("Index", "HenBatch", new { henStage = area, validationError = localizer[Lang.NoDestinations].Value });
                else if (onlyEggs)
                    return RedirectToAction("Index", "MaterialExitReport", new { area = area, validationError = error, destinationArea = AreaEnum.Classification });
                else
                    return RedirectToAction("Index", "MaterialExitReport", new { area = area, validationError = error });
            }

            ViewData["HasBatches"] = originId != null ? materialExitReportBusinessLogic.HasBatches(new Guid(originId)) : false;

            return View("Create", model);
        }

        [HttpPost]
        public async Task<IActionResult> Create(MaterialExitReportViewModel model)
        {
            AreaEnum area = EnumHelper<AreaEnum>.Parse(model.Area);
            bool birdsAreSent = materialExitReportBusinessLogic.HasBatches(new Guid(model.OriginId));

            // Check if model is valid
            if (ModelState.IsValid)
            {
                // set base relative values 
                model = SetRelativeValues(model);
                MaterialExitReport entity = model.ToEntity();

                try
                {
                    await materialExitReportBusinessLogic.CreateMaterialExitReport(entity, model.ExitMaterials.Select(em => em.ToDTO()).ToList(), model.ToDataDTO(), area, model.OnlyEggs);
                    return Redirect(model.ReferURL);
                }
                catch (ValidationException ex)
                {
                    foreach (KeyValuePair<string, string> error in ex.Errors)
                        ModelState.AddModelError(string.Empty, error.Value);
                }
            }

            // Setup View Title and Mode
            ViewData["Title"] = localizer[Lang.CreateTitle];
            ViewData["Action"] = "Create";
            ViewData["MaterialExitReportResources"] = JsLocalizer.GetLocalizedResources(JsLang.MaterialExitReportCreate, this.localizer);
            ViewData["BirdsAreSent"] = birdsAreSent;
            ViewData["HasBatches"] = !string.IsNullOrEmpty(model.OriginId) ? materialExitReportBusinessLogic.HasBatches(new Guid(model.OriginId)) : false;

            InitLists(model);
            // create the materials rows 
            foreach (ExitMaterialViewModel material in model.ExitMaterials)
            {
                if (!string.IsNullOrEmpty(material.MaterialId))
                {
                    material.Materials = materialExitReportBusinessLogic.GetMaterialsForRows(model.OriginId, model.PersonDestinationId, model.DestinationId, null, material.MaterialId);
                    if (model.Area == AreaEnum.Classification.ToString() || model.OnlyEggs || containerService.GetWithAreaContainers(new Guid(model.OriginId)).AreaContainers.Any(ac => ac.AreaEnum == AreaEnum.Classification) || birdsAreSent)
                        material.MeasurementUnits = capacityUnitService.GetByMeasurementUnitType(MeasurementUnitType.Unit)
                                                                    .OrderBy(t => t.RelativeValue)
                                                                    .Select(cu => new SelectListItem(cu.Symbol, cu.Id.ToString(), cu.Id.ToString() == material.MeasurementUnitId))
                                                                    .ToList();
                    else
                        material.MeasurementUnits = capacityUnitService.GetByMeasurementUnitType(MeasurementUnitType.Weight)
                                                                    .OrderBy(t => t.RelativeValue)
                                                                    .Select(cu => new SelectListItem(cu.Symbol, cu.Id.ToString(), cu.Id.ToString() == material.MeasurementUnitId))
                                                                    .ToList();
                    material.MaterialBatches = materialExitReportBusinessLogic.GetMaterialBatches(model.OriginId, material.MaterialId, material.MaterialBatchId, model.ExitMaterials.Select(em => em.MaterialBatchId) as string[]);
                }
                else
                {
                    material.Materials = materialExitReportBusinessLogic.GetMaterialsForRows(model.OriginId, model.PersonDestinationId, model.DestinationId, null, null);
                    material.MeasurementUnits = new List<SelectListItem>();
                    if (material.Materials.Count() == 1)
                        material.MeasurementUnits = GetUnits(material.Materials.FirstOrDefault().Value);
                }
            }
            return View("Create", model);
        }

        private string InitLists(MaterialExitReportViewModel model)
        {
            List<Container> origins = new List<Container>();
            List<SelectListItem> destinations = new List<SelectListItem>();
            (List<SelectListItem>, List<Container>) destinationLists = (new List<SelectListItem>(), new List<Container>());
            List<Person> personDB = new List<Person>();
            AreaEnum area = EnumHelper<AreaEnum>.Parse(model.Area);
            Guid modelOriginId = !string.IsNullOrEmpty(model.OriginId) ? new Guid(model.OriginId) : Guid.Empty;

            // origin options
            if ((area == AreaEnum.Breeding || area == AreaEnum.Laying) && !string.IsNullOrEmpty(model.OriginId) && containerService.Get(modelOriginId).ContainerType == ContainerTypes.HenBatch)
                origins = containerService.GetAllByAreaWithMaterials(area, addMaterials: false, asNoTracking: false).Where(c => c.Id.ToString() == model.OriginId).ToList();
            else if (area == AreaEnum.Classification && !model.OnlyEggs)
                origins = containerService.GetAllByAreaWithMaterials(area, addMaterials: false, asNoTracking: true).Where(o => o.ContainerType == ContainerTypes.StorageWarehouse).ToList();
            else if (!string.IsNullOrEmpty(model.Area) && !model.OnlyEggs)
                origins = containerService.GetAllByAreaWithMaterials(area, asNoTracking: true).Where(c => c.MaterialContainers.Any(mc => mc.Quantity > 0)).ToList();
            else if (model.OnlyEggs)
                origins = containerService.GetAllByAreaWithMaterials(AreaEnum.Laying, asNoTracking: true).Where(c => c.ContainerType != ContainerTypes.Line && c.ContainerType != ContainerTypes.HenBatch
                                                                                    && (c.MaterialContainers.Any(mc => mc.Quantity > 0 && mc.Material.MaterialType.Path.Contains(MaterialTypePaths.InsumoMateriaPrimaHuevo))
                                                                                    || c.AcceptedMaterialType.Any(amt => amt.MaterialType.Path.Contains(MaterialTypePaths.InsumoMateriaPrimaHuevo) && (amt.ActionEnum == ActionsEnum.Produce || amt.ActionEnum == ActionsEnum.ConsumeAndProduce)))).ToList();

            // person destination options
            if (!string.IsNullOrEmpty(model.PersonDestinationId))
                personDB.Add(personService.Get(new Guid(model.PersonDestinationId)));
            else if (!string.IsNullOrEmpty(model.Area))
                personDB = personService.GetPersonByArea(area).Where(p => p.PersonRole == PersonRoleEnum.Customer).ToList();
            else if (!string.IsNullOrEmpty(model.OriginId))
            {
                List<AreaContainer> areas = containerService.GetWithAreaContainers(modelOriginId).AreaContainers;
                foreach (AreaContainer a in areas)
                    personDB.Concat(personService.GetPersonByArea(a.AreaEnum));
            }


            // destination options
            if (!string.IsNullOrEmpty(model.OriginId) && model.OriginIsHenBatch)
            {
                string lineId = henBatchService.GetAll(asNoTracking: true)
                                            .Where(hb => hb.Id == modelOriginId)
                                            .Select(hb => hb.LineId)
                                            .FirstOrDefault().ToString();
                destinationLists = materialExitReportBusinessLogic.GetDestinations(lineId, model.DestinationId, model.Area, model.OriginIsHenBatch, model.OnlyEggs);
                destinations = destinationLists.Item1;
            }
            else if (!string.IsNullOrEmpty(model.OriginId) && string.IsNullOrEmpty(model.DestinationId))
            {
                destinationLists = materialExitReportBusinessLogic.GetDestinations(model.OriginId, model.DestinationId, model.Area, false, model.OnlyEggs);
                destinations = destinationLists.Item1;
            }
            else if (!string.IsNullOrEmpty(model.OriginId) && !string.IsNullOrEmpty(model.DestinationId))
            {
                var destinationsContainersList = new List<Container> { containerService.Get(new Guid(model.DestinationId)) };
                destinations = destinationsContainersList.Select(d => new SelectListItem(d.DetailedName, d.Id.ToString(), d.Id.ToString() == model.DestinationId)).ToList();

            }
            else if (origins.Count() == 1 && area == AreaEnum.Classification)
            {
                destinationLists = materialExitReportBusinessLogic.GetDestinations(origins.FirstOrDefault().Id.ToString(), model.DestinationId, model.Area, false, model.OnlyEggs);
                destinations = destinationLists.Item1;
            }
            else if (origins.Count() == 1)
            {
                destinationLists = materialExitReportBusinessLogic.GetDestinations(origins.FirstOrDefault().Id.ToString(), model.DestinationId, null, false, model.OnlyEggs);
                destinations = destinationLists.Item1;
            }

            // see if its posible to create a report
            if (!string.IsNullOrEmpty(model.Area) && !origins.Any()) // there are no containers for the selected area
                return localizer[Lang.NoOriginContainers];
            else if (!model.OnlyEggs && area != AreaEnum.Classification && (!string.IsNullOrEmpty(model.OriginId) || origins.Count() == 1) && !destinations.Any() && !personDB.Any()) // no destination for the selected origin
                return "1";
            else if ((!string.IsNullOrEmpty(model.OriginId) || origins.Count() == 1) && !destinations.Any() && !personDB.Any()) // no destination for the selected origin
                return localizer[Lang.NoDestinations];

            var containersDestinations = destinationLists.Item2;

            IQueryable<Guid> slaughterhouses = slaughterhouseService.GetAll().Select(s => s.Id);

            var slaughterhouse = containersDestinations.Where(d => slaughterhouses.Contains(d.Id));

            if (containersDestinations.Any(cd => cd.ContainerType == ContainerTypes.Slaughterhouse) && (slaughterhouse == null || !slaughterhouse.Any()))
            {
                return localizer[Lang.NoSlaughterhouse];
            }

            ViewData["Origins"] = origins.OrderBy(o => o.DetailedName)
                .Select(o => new SelectListItem(o.DetailedName, o.Id.ToString(), o.Id.ToString() == model.OriginId || origins.Count == 1))
                .ToList();

            ViewData["Persons"] = personDB.OrderBy(p => p.Name)
                .Select(p => new SelectListItem(p.Name, p.Id.ToString(), p.Id.ToString() == model.PersonDestinationId || personDB.Count == 1))
                .ToList();

            if (personDB.Count == 1)
            {
                model.PersonDestinationId = personDB.First().Id.ToString();
            }

            ViewData["Destinations"] = destinations;

            if (destinations.Count == 1)
            {
                model.DestinationId = destinations.First().Value;
            }

            ViewData["Origin"] = destinations.Count > 0 && personDB.Count == 0
                ? MovementTypes.Internal
                : destinations.Count == 0 && personDB.Count > 0
                    ? MovementTypes.External
                    : default;

            // no errors 
            return "";
        }
        #endregion

        #region Details 
        public IActionResult Details(string id, AreaEnum? area = null, AreaEnum? destinationArea = null)
        {
            try
            {
                ViewData["Title"] = localizer[Lang.DetailsTitle];
                ViewData["Area"] = area.HasValue ? area.Value.ToString() : "";
                ViewData["DestinationArea"] = destinationArea.HasValue ? destinationArea.Value.ToString() : "";
                // Get materialExitReport from database 
                MaterialExitReportViewModel model = new MaterialExitReportViewModel(this.materialExitReportService.GetFull(new Guid(id)))
                {
                    Area = area.HasValue ? area.Value.ToString() : ""
                };
                // Return view with materialExitReport info
                return View(model);
            }
            catch (NotFoundException)
            {
                return NotFound();
            }
        }
        #endregion

        #region Data 
        public List<SelectListItem> GetDestinations(string originId, string selected, string area = null, bool originIsHenBatch = false, bool onlyEggs = false)
            => materialExitReportBusinessLogic.GetDestinations(originId, selected, area, originIsHenBatch, onlyEggs).Item1;

        public List<SelectListItem> GetUnits(string materialId)
        {
            Material material = materialService.GetFull(new Guid(materialId));
            return capacityUnitService.GetByMeasurementUnitType(material.CapacityUnit.MeasurementUnitType).OrderBy(t => t.RelativeValue).Select(cu => new SelectListItem(cu.Symbol, cu.Id.ToString(), cu.Id == material.CapacityUnitId)).ToList();
        }

        [HttpPost]
        public IActionResult GetMaterialRow(MaterialReceptionInputRow data)
        {
            MaterialExitReportViewModel model = new MaterialExitReportViewModel();
            List<SelectListItem> materials = materialExitReportBusinessLogic.GetMaterialsForRows(data.OriginId, data.PersonId, data.DestinationId, data.Ids, null);
            List<SelectListItem> measures = new List<SelectListItem>();
            List<SelectListItem> batches = new List<SelectListItem>();

            decimal batchQuantity = 0;

            if (materials.Count() == 1)
            {
                measures = GetUnits(materials.FirstOrDefault().Value);
                batches = materialExitReportBusinessLogic.GetMaterialBatches(data.OriginId, materials.FirstOrDefault().Value, null, data.Ids);
                if (batches.Count() == 1)
                    batchQuantity = GetMaterialBatchQuantity(data.OriginId, batches.FirstOrDefault().Value);
            }
            else if (!materials.Any())
            {
                this.HttpContext.Response.StatusCode = (int)HttpStatusCode.NotFound;
                return new JsonResult(new
                {
                    exceptionManager.Handle(new UserException(this.localizer[Lang.NoMaterialAvailable])).Message
                });
            }

            bool MaterialTypeEgg = data.OnlyEggs
                ? data.OnlyEggs
                : data.Area.HasValue
                        ? containerService.GetWithAreaContainers(new Guid(data.OriginId)).AreaContainers.Any(ac => ac.AreaEnum == AreaEnum.Classification)
                        : data.Area == AreaEnum.Classification;

            for (int i = 0; i < data.RowsLength; i++)
                model.ExitMaterials.Add(new ExitMaterialViewModel()
                {
                    Materials = materials,
                    MeasurementUnits = measures,
                    MaterialBatches = batches,
                    MaterialTypeEgg = MaterialTypeEgg,
                    BatchQuantity = batchQuantity
                });

            if (MaterialTypeEgg)
                return ViewComponent("EggsMaterialsExit", new { index = data.RowsLength - 1, model });
            else
            {
                bool hasBatches = !string.IsNullOrEmpty(data.OriginId) ? materialExitReportBusinessLogic.HasBatches(new Guid(data.OriginId)) : false;
                return ViewComponent("MaterialsExit", new { index = data.RowsLength - 1, model, hasBatches });
            }
        }

        public int GetNumberRowsDefault(Guid? origin = null, Guid? personDestination = null, Guid? destination = null)
            => materialExitReportBusinessLogic.GetAmountAcceptedMaterials(origin, personDestination, destination);

        public List<SelectListItem> GetMaterialBatches(MaterialReceptionInputRow data)
            => materialExitReportBusinessLogic.GetMaterialBatches(data.OriginId, data.MaterialId, data.MaterialBatchId, data.Ids);

        public decimal GetMaterialBatchQuantity(string originId, string batchId)
            => decimal.Round(containerService.GetAvailableMaterialBatches(new Guid(originId)).FirstOrDefault(mb => mb.MaterialBatchId.ToString() == batchId).Quantity, 0);

        public bool IsHatchable(string materialId)
        {
            MaterialType materialtype = materialService.GetWithMaterialType(new Guid(materialId)).MaterialType;
            return materialtype.Path.Contains(MaterialTypePaths.InsumoMateriaPrimaHuevosHuevoSinClasificarIncubable)
                || materialtype.Path.Contains(MaterialTypePaths.InsumoMateriaPrimaHuevosHuevoClasificadoIncubable);
        }

        public bool IsMaterialFood(string materialId)
            => materialService.GetWithMaterialType(new Guid(materialId)).MaterialType.Id == MaterialTypes.InsumoMateriaPrimaAlimentacionFormula;

        private MaterialExitReportViewModel SetRelativeValues(MaterialExitReportViewModel model)
        {
            model.ExitMaterials = model.ExitMaterials.Where(d => !d.Deleted).ToList();

            foreach (ExitMaterialViewModel material in model.ExitMaterials)
            {
                if (!material.Deleted)
                {
                    material.Quantity = capacityUnitBusinessLogic.GetBaseRelativeValue(new Guid(material.MeasurementUnitId), material.Quantity);
                    if (model.Area == AreaEnum.Classification.ToString())
                    {
                        material.UnbrokenEggQuantity = capacityUnitBusinessLogic.GetBaseRelativeValue(new Guid(material.MeasurementUnitId), material.UnbrokenEggQuantity);
                        material.BrokenEggQuantity = capacityUnitBusinessLogic.GetBaseRelativeValue(new Guid(material.MeasurementUnitId), material.BrokenEggQuantity);
                    }

                }
            }

            return model;
        }

        #endregion

        #region Reporting
        public IActionResult DownloadSendEggsReport(Guid id)
        {
            SendEggsReport report = materialExitReportBusinessLogic.GenerateCurrentEggReport(id);
            using MemoryStream ms = new MemoryStream();
            report.ExportToPdf(ms);
            return File(ms.ToArray(), "application/pdf", report.ExportOptions.Pdf.DocumentOptions.Title + ".pdf");
        }

        #endregion

        private async Task<List<object>> GetIdentifiers()
        {
            return await materialExitReportBusinessLogic.GetShippingNotesIdentifiersAsync();
        }

        private bool CheckAuthorizationForAdministrator(AreaEnum? area = null)
        {
            if (area.HasValue)
            {
                switch (area)
                {
                    case AreaEnum.Laying:
                        if (!this.operationContext.UserIsInAnyRole(
                            Roles.BackofficeSuperAdministrator,
                            Roles.BackofficeStockAdministrator,
                            Roles.BackofficeLayingAdministrator,
                            Roles.BackofficeLayingStockAdministrator))
                            return false;
                        break;

                    case AreaEnum.Breeding:
                        if (!this.operationContext.UserIsInAnyRole(
                            Roles.BackofficeSuperAdministrator,
                            Roles.BackofficeStockAdministrator,
                            Roles.BackofficeBreedingAdministrator,
                            Roles.BackofficeBreedingStockAdministrator))
                            return false;
                        break;

                    case AreaEnum.Classification:
                        if (!this.operationContext.UserIsInAnyRole(
                            Roles.BackofficeSuperAdministrator,
                            Roles.BackofficeStockAdministrator,
                            Roles.BackofficeClassificationAdministrator,
                            Roles.BackofficeClassificationStockAdministrator))
                            return false;
                        break;

                    case AreaEnum.Dispatch:
                        if (!this.operationContext.UserIsInAnyRole(
                            Roles.BackofficeSuperAdministrator,
                            Roles.BackofficeStockAdministrator,
                            Roles.BackofficeDispatchAdministrator,
                            Roles.BackofficeDispatchStockAdministrator))
                            return false;
                        break;

                    case AreaEnum.FeedFactory:
                        if (!this.operationContext.UserIsInAnyRole(
                            Roles.BackofficeSuperAdministrator,
                            Roles.BackofficeStockAdministrator,
                            Roles.BackofficeFeedFactoryAdministrator,
                            Roles.BackofficeFeedFactoryStockAdministrator))
                            return false;
                        break;
                    case AreaEnum.Packing:
                        if (!this.operationContext.UserIsInAnyRole(
                            Roles.BackofficeSuperAdministrator,
                            Roles.BackofficeStockAdministrator,
                            Roles.BackofficePackingAdministrator,
                            Roles.BackofficePackingStockAdministrator))
                            return false;
                        break;

                    case AreaEnum.HealthCare:
                        if (!this.operationContext.UserIsInAnyRole(
                            Roles.BackofficeSuperAdministrator,
                            Roles.BackofficeStockAdministrator,
                            Roles.BackofficeHealthCareAdministrator,
                            Roles.BackofficeHealthCareStockAdministrator))
                            return false;
                        break;

                    default:
                        if (!this.operationContext.UserIsInAnyRole(
                            Roles.BackofficeSuperAdministrator,
                            Roles.BackofficeStockAdministrator))
                            return false;
                        break;
                };
                return true;
            }
            else
            {
                if (!this.operationContext.UserIsInAnyRole(
                            Roles.BackofficeSuperAdministrator,
                            Roles.BackofficeStockAdministrator))
                    return false;
                else
                    return true;
            }
        }

        private bool CheckAuthorizationForUsers(AreaEnum? area = null)
        {
            if (area.HasValue)
            {
                switch (area)
                {
                    case AreaEnum.Laying:
                        if (!this.operationContext.UserIsInAnyRole(
                            Roles.BackofficeSuperAdministrator,
                            Roles.BackofficeStockAdministrator,
                            Roles.BackofficeLayingAdministrator,
                            Roles.BackofficeLayingStockAdministrator,
                            Roles.BackofficeStockUser,
                            Roles.BackofficeLayingStockUser))
                            return false;
                        break;

                    case AreaEnum.Breeding:
                        if (!this.operationContext.UserIsInAnyRole(
                            Roles.BackofficeSuperAdministrator,
                            Roles.BackofficeStockAdministrator,
                            Roles.BackofficeBreedingAdministrator,
                            Roles.BackofficeBreedingStockAdministrator,
                            Roles.BackofficeStockUser,
                            Roles.BackofficeBreedingStockUser))
                            return false;
                        break;

                    case AreaEnum.Classification:
                        if (!this.operationContext.UserIsInAnyRole(
                            Roles.BackofficeSuperAdministrator,
                            Roles.BackofficeStockAdministrator,
                            Roles.BackofficeClassificationAdministrator,
                            Roles.BackofficeClassificationStockAdministrator,
                            Roles.BackofficeStockUser,
                            Roles.BackofficeClassificationStockUser))
                            return false;
                        break;

                    case AreaEnum.Dispatch:
                        if (!this.operationContext.UserIsInAnyRole(
                            Roles.BackofficeSuperAdministrator,
                            Roles.BackofficeStockAdministrator,
                            Roles.BackofficeDispatchAdministrator,
                            Roles.BackofficeDispatchStockAdministrator,
                            Roles.BackofficeStockUser,
                            Roles.BackofficeDispatchStockUser))
                            return false;
                        break;

                    case AreaEnum.FeedFactory:
                        if (!this.operationContext.UserIsInAnyRole(
                            Roles.BackofficeSuperAdministrator,
                            Roles.BackofficeStockAdministrator,
                            Roles.BackofficeStockUser,
                            Roles.BackofficeFeedFactoryAdministrator,
                            Roles.BackofficeFeedFactoryStockAdministrator,
                            Roles.BackofficeFeedFactoryStockUser))
                            return false;
                        break;
                    case AreaEnum.Packing:
                        if (!this.operationContext.UserIsInAnyRole(
                            Roles.BackofficeSuperAdministrator,
                            Roles.BackofficeStockAdministrator,
                            Roles.BackofficePackingAdministrator,
                            Roles.BackofficePackingStockAdministrator,
                            Roles.BackofficeStockUser,
                            Roles.BackofficePackingStockUser))
                            return false;
                        break;

                    case AreaEnum.HealthCare:
                        if (!this.operationContext.UserIsInAnyRole(
                            Roles.BackofficeSuperAdministrator,
                            Roles.BackofficeStockAdministrator,
                            Roles.BackofficeHealthCareAdministrator,
                            Roles.BackofficeHealthCareStockAdministrator,
                            Roles.BackofficeStockUser,
                            Roles.BackofficeHealthCareStockUser))
                            return false;
                        break;

                    default:
                        if (!this.operationContext.UserIsInAnyRole(
                            Roles.BackofficeSuperAdministrator,
                            Roles.BackofficeStockAdministrator,
                            Roles.BackofficeStockUser))
                            return false;
                        break;
                };
                return true;
            }
            else
            {
                if (!this.operationContext.UserIsInAnyRole(
                            Roles.BackofficeSuperAdministrator,
                            Roles.BackofficeStockAdministrator,
                            Roles.BackofficeStockUser))
                    return false;
                else
                    return true;
            }
        }
    }
}

